网上开户/线上/柜面业务提交审核时间分布：set RQ=${RQ};

--包含指标

--XSYY_WSKHTJSHSD 网上开户提交审核时段
--XSYY_XSYWTJSHSJFB 线上业务提交审核时间分布
--JZYY_YWTJSHSJFB 柜面业务提交审核时间分布

-- 网上开户提交审核时段
create temporary view temp_cif_wskhtjshsd as 
SELECT
  ${RQ} AS rq,
  c.id AS idx_id,
  a.fldm AS zddm,
  a.ibm AS ibm,
  a.note AS zdmc,
  c.idx_code AS idx_code,
  COALESCE(cast(b.result as DECIMAL(20,4)), 0) AS `result`
FROM
  TXTDM_mapping a left join
  (
    SELECT
      CAST(SUBSTRING(sqsj, 1, 2) AS INT) AS sd,
      COUNT(*) AS result
    FROM
      LCFXCKH_mapping
    where sqrq = ${RQ} and step not in (-9,0)
    GROUP BY
      CAST(SUBSTRING(sqsj, 1, 2) AS INT)
  ) b on b.sd = a.ibm
  join TIC_ZBCS_mapping c
WHERE
  a.fldm = 'TIC_TIME' and c.idx_code = 'XSYY_WSKHTJSHSD';


-- 线上业务提交审核时间分布
create temporary view temp_cif_xsywtjshsjfb_1 as 
select 
    count(1) as zbz, t.sjd
from 
    (
        select 
            hour(b.sqsj) as sjd 
        from (
            select 
                id,sqsj
            from 
                TYWQQ_mapping t 
            where 
                t.blms = 2 
                and t.sqrq = ${RQ} 
                and t.ywdm not in ('00003') 
                and t.clzt <> 0
                and t.ywdm in (select cbm from TXTDM_mapping where flag in (2,3) and fldm = 'CIF_YWDM') 
                and gllc is not null 
            union
            select 
                id,sqsj
            from 
                TYWQQ_mapping tq2  
            where 
                tq2.blms = 2 
                and tq2.sqrq = ${RQ} 
                and tq2.ywdm not in ('00003') 
                and tq2.clzt <> 0
                and tq2.jzr is not null
        ) b
    ) t
group by t.sjd;

create temporary view temp_cif_xsywtjshsjfb_2 as 
select 
    ifnull(zbz,0) as zbz, t.fldm as zddm, t.ibm, t.note as zdmc 
from 
    TXTDM_mapping t 
    left join temp_cif_xsywtjshsjfb_1 a on a.sjd = t.IBM  
where 
    t.fldm='TIC_TIME';

create temporary view temp_cif_xsywtjshsjfb_3 as 
select 
    ${RQ} as rq,
    a.id as idx_id,
    b.zddm,
    b.ibm,
    b.zdmc,
    a.idx_code as idx_code,
    b.zbz as result
from 
    TIC_ZBCS_mapping a,
    (select 'XSYY_XSYWTJSHSJFB' idx_code,zbz,zddm,ibm,zdmc from temp_cif_xsywtjshsjfb_2) b
where 
    a.idx_code = b.idx_code
    and a.idx_code='XSYY_XSYWTJSHSJFB';

-- 柜面业务提交审核时间分布
create temporary view temp_cif_gmywtjshsjfb_1 as
SELECT 
    t.sjd, COUNT(1) AS zbz
FROM 
    (
        SELECT 
            id, hour(t.sqsj) AS sjd
        FROM 
            TYWQQ_mapping t
        WHERE 
            t.sqrq = ${RQ}
            and t.clzt <> 0
            and t.ywdm in (select cbm from TXTDM_mapping where flag in (1,3) and fldm = 'CIF_YWDM') 
            and t.ywdm <> '50101'
            and (t.fqqd = 1 or (t.ywdm = '20042' and t.fqqd = 11))
    ) t
GROUP BY t.sjd;

create temporary view temp_cif_gmywtjshsjfb_2 as 
select 
    ifnull(zbz,0) as zbz, t.fldm as zddm, t.ibm, t.note as zdmc 
from 
    TXTDM_mapping t 
    left join temp_cif_gmywtjshsjfb_1 a on a.sjd = t.IBM  
where 
    t.fldm='TIC_TIME';

create temporary view temp_cif_gmywtjshsjfb_3 as 
select 
    ${RQ} as rq,
    a.id as idx_id,
    b.zddm,
    b.ibm,
    b.zdmc,
    a.idx_code as idx_code,
    b.zbz as result
from 
    TIC_ZBCS_mapping a,
    (select 'JZYY_YWTJSHSJFB' idx_code,zbz,zddm,ibm,zdmc from temp_cif_gmywtjshsjfb_2) b
where 
    a.idx_code = b.idx_code
    and a.idx_code='JZYY_YWTJSHSJFB';


upsert into TIC_TYZDXZB_mapping
select * from temp_cif_wskhtjshsd
union all
select * from temp_cif_xsywtjshsjfb_3
union all
select * from temp_cif_gmywtjshsjfb_3
;


网上开户办理统计：SET RQ=${RQ};
 
-- 创建临时视图
CREATE TEMPORARY VIEW temp_jrwskhtj AS 
SELECT * FROM LCFXCKH_mapping WHERE sqrq = ${RQ} AND step IN (1,2,3,5,6,99);

-- 双向视频提交数
create temporary view temp_xsyy_sxsptjs as 
select 
    (lc1.num1 - lc2.num2) as result
from
(
    select 
        count(1) num1 
    from 
        (
            select 
                zjbh
            FROM 
                LCFXCKH_mapping
            WHERE 
                splx = 0 and sqrq = ${RQ} and step in (1,2,3,5,6,99) 
            group by 
                zjlb,zjbh,khqc
        ) lcfxckh
) lc1,   
(
    select 
        count(1) as num2
    from
        (
            select 
                zjbh
            from
                (
                    select 
                        b.zjlb,b.zjbh,b.khqc,b.splx,b.step
                    from 
                        LCFXCKH_mapping a,
                        LCFXCKH_mapping b
                    where 
                        a.sqrq = ${RQ} and a.step in (1,2,3,5,6,99) and a.splx = 0
                        and b.sqrq = ${RQ} and b.step in (1,2,3,5,6,99) and b.splx = 1
                        and a.zjlb = b.zjlb and a.zjbh = b.zjbh and a.khqc = b.khqc 
                ) c
            where 
                step <> 99
            group by zjlb,zjbh,khqc
        ) d
) lc2    
;

-- 单向视频提交数
create temporary view temp_xsyy_dxsptjs as 
select 
    (lc1.num1 - lc2.num2) as result
from
(
    select 
        count(1) num1 
    from 
        (
            select 
                zjbh
            FROM 
                LCFXCKH_mapping
            WHERE 
                splx = 1 and sqrq = ${RQ} and step in (1,2,3,5,6,99) 
            group by 
                zjlb,zjbh,khqc
        ) lcfxckh
) lc1,   
(
    select 
        count(1) as num2
    from
        (
            select 
                zjbh
            from
                (
                    select 
                        b.zjlb,b.zjbh,b.khqc,b.splx,b.step
                    from 
                        LCFXCKH_mapping a,
                        LCFXCKH_mapping b
                    where 
                        a.sqrq = ${RQ} and a.step in (1,2,3,5,6,99) and a.splx = 0
                        and b.sqrq = ${RQ} and b.step in (1,2,3,5,6,99) and b.splx = 1
                        and a.zjlb = b.zjlb and a.zjbh = b.zjbh and a.khqc = b.khqc 
                ) c
            where 
                step <> 99
            group by zjlb,zjbh,khqc
        ) d
) lc2    
;

create temporary view temp_xsyy_khtemp as 
select 
    t1.khfs,t2.step
from 
    TKHXX_mapping t1
    left join LCFXCKH_mapping t2 on t1.khh = t2.gtkhh
where 
    t1.khrq = ${RQ} AND khzt <> 99;

-- 今日网上开户成功数
create temporary view temp_xsyy_jrwskhcgs as 
select
  ${RQ} as rq,
  t.id as idx_id,
  t.idx_code as idx_code,
  cast(a.result as DECIMAL(20,4)) as result
from
  (
    select 
      count(1) as result
    from 
      temp_xsyy_khtemp
    where 
      khfs in (3,5) and step IN (5,6)
  ) as a,
  TIC_ZBCS_mapping t
where
  t.idx_code = 'XSYY_JRWSKHCGS';

-- 今日网上开户成功率
create temporary view temp_xsyy_jrwskhcgl as 
select
  ${RQ} as rq,
  t.id as idx_id,
  t.idx_code as idx_code,
  cast(a.result as DECIMAL(20,4)) as result
from
  (
    SELECT 
        CASE
           WHEN w.tjs = 0 THEN 0
           ELSE round(x.cgs / w.tjs,4)
        END AS RESULT
    FROM 
        (
            select 
                count(1) as cgs
            from 
                temp_xsyy_khtemp
            where 
                khfs in (3,5) and step IN (5,6)
        ) x, 
        (
            select count(*) tjs 
            from (
                select zjbh
                FROM LCFXCKH_mapping
                WHERE sqrq = ${RQ} and step in (1,2,3,5,6,99) 
                group by zjlb,zjbh,khqc
            ) x
        ) w
  ) as a,
  TIC_ZBCS_mapping t
where
  t.idx_code = 'XSYY_JRWSKHCGL';

-- 今日公司开户成功数
create temporary view temp_xsyy_jrgskhcgs as 
select
  ${RQ} as rq,
  t.id as idx_id,
  t.idx_code as idx_code,
  cast(a.result as DECIMAL(20,4)) as result
from
  (
    select 
      count(1) as result
    from 
      temp_xsyy_khtemp
    where 
      step in (5,6) OR khfs in (1,4)
  ) as a,
  TIC_ZBCS_mapping t
where
  t.idx_code = 'XSYY_KHCGBS';

-- 今日网上开户占比
create temporary view temp_xsyy_jrwskhzb as 
select
  ${RQ} as rq,
  t.id as idx_id,
  t.idx_code as idx_code,
  --cast(a.result as DECIMAL(20,4)) as result
  a.result as result
from
  (
    SELECT 
        CASE
            WHEN y.khcgbs = 0 THEN 0
            ELSE x.wskhcgs / y.khcgbs
        END AS RESULT
    FROM 
        (
            select 
                count(1) as wskhcgs
            from 
                temp_xsyy_khtemp
            where 
                khfs in (3,5) and step IN (5,6) 
        ) x, 
        (
            select 
                count(1) as khcgbs
            from 
                temp_xsyy_khtemp
            where 
                 step in (5,6) OR khfs in (1,4)  
        ) y
  ) as a,
  TIC_ZBCS_mapping t
where
  t.idx_code = 'XSYY_JRWSKHZB';

-- 单向视频占比
create temporary view temp_xsyy_dxspzb as 
select
  ${RQ} as rq,
  t.id as idx_id,
  t.idx_code as idx_code,
  cast(a.result as DECIMAL(20,4)) as result
from
  (
    SELECT 
        CASE
            WHEN y.total = 0 THEN 0
            ELSE round(x.dxsps / y.total, 4)
        END AS RESULT
    FROM
        (
            select 
                count(1) as dxsps
            from 
                temp_xsyy_khtemp    
            where 
                khfs in (5) and step in (5,6)  
        ) x,
        (
            select 
                count(1) as total
            from 
                temp_xsyy_khtemp
            where 
                khfs in (3,5) and step in (5,6)
        ) y
  ) as a,
  TIC_ZBCS_mapping t
where
  t.idx_code = 'XSYY_DXSPZB';
 
-- 执行查询并合并结果
CREATE TEMPORARY VIEW temp_results AS
SELECT 'XSYY_JRWSKHTJS' AS indicator, COUNT(*) AS result FROM (SELECT zjbh FROM temp_jrwskhtj GROUP BY zjlb,zjbh,khqc) x
UNION ALL
SELECT 'XSYY_SXSPTJS' AS indicator, result FROM temp_xsyy_sxsptjs
UNION ALL
SELECT 'XSYY_DXSPTJS' AS indicator, result FROM temp_xsyy_dxsptjs
UNION ALL
SELECT 'XSYY_WSKHSHS' AS indicator, COUNT(1) AS result FROM TYWQQ_CZLS_mapping WHERE (INSTR(jdmc,'非现初审')>0 OR INSTR(jdmc,'非现终审')>0) AND czrq = ${RQ}
UNION ALL
SELECT 'XSYY_WSKHFHS' AS indicator, COUNT(1) AS result FROM TYWQQ_CZLS_mapping WHERE INSTR(jdmc,'非现复审')>0 AND czrq = ${RQ}
UNION ALL
SELECT 'XSYY_WSKHSHBTGS' AS indicator, COUNT(1) AS result FROM TYWQQ_CZLS_mapping WHERE (jdmc = '非现初审终止办理' OR jdmc = '非现终审终止办理') AND czrq = ${RQ}
UNION ALL
SELECT 'XSYY_WSKHFHBTGS' AS indicator, COUNT(1) AS result FROM TYWQQ_CZLS_mapping WHERE jdmc = '非现复审终止办理' AND czrq = ${RQ};
 
create temporary view total as 
select
  ${RQ} as rq,
  t.id as idx_id,
  t.idx_code as idx_code,
  cast(a.result as DECIMAL(20,4)) as result
from
   temp_results a,TIC_ZBCS_mapping t
where
  t.idx_code = a.indicator;

upsert into TIC_YXZB_mapping 
select * from total
union all 
select * from temp_xsyy_jrwskhcgs
union all
select * from temp_xsyy_jrwskhcgl
union all
select * from temp_xsyy_jrgskhcgs
union all
select * from temp_xsyy_jrwskhzb
union all
select * from temp_xsyy_dxspzb
;



网上开户分支机构开户统计：set RQ=${RQ};

--包含指标
--XSYY_YYBWSKHSHS 各分支机构网上开户审核数top5
--XSYY_YYBWSKHDXWCS 各分支机构网上开户单向完成数
--XSYY_YYBWSKHWCS 各分支机构网上开户完成数top5
--XSYY_YYBWSKHDXKHZB 各分支机构网上开户单向开户占比top5

-- 各分支机构网上开户审核数top5
create temporary view temp_xsyy_gfzjgwskhshs as 
select
  ${RQ} as rq,
  t.id as idx_id,
  a.yyb as yyb,
  t.idx_code as idx_code,
  cast(a.result as DECIMAL(20, 4)) as result
from
  (
     select
        a.id as yyb,
        nvl(c.result,0) AS result
    from
        LBORGANIZATION_mapping a
        left join TXTDM_mapping b on a.orgtype = b.ibm
        left join (
            select 
                tq.yyb as yyb,count(1) as result
            from 
                TYWQQ_CZLS_mapping ls
                join TYWQQ_mapping tq on ls.ywqqid =tq.id
            where 
                (instr(ls.jdmc,'非现初审')>0 or instr(ls.jdmc,'非现终审')>0) and ls.czrq = ${RQ}
            group by tq.yyb
        ) c on a.id = c.yyb
    where
        b.fldm = 'LB.ORGANIZATION' and a.orgtype = 3 and a.id not in (1052,1058,1088,4444)
  ) as a,
  TIC_ZBCS_mapping t
where
  t.idx_code = 'XSYY_YYBWSKHSHS';

-- 各分支机构网上开户单向完成数/各分支机构网上开户完成数top5=各分支机构网上开户单向开户占比top5

create temporary view temp_xsyy_gfzjgwskhdxkh as 
SELECT
	a.id AS YYB,
	COUNT(CASE WHEN c.khfs = 5 THEN 1 END) AS dxwcs,
	count(a.id) AS wskhwcs,
	COALESCE(round(COUNT(CASE WHEN c.khfs = 5 THEN 1 END) / NULLIF(COUNT(CASE WHEN c.khfs in (3,5) THEN 1 END),0),4), 0) AS dxkhzb
FROM
	LBORGANIZATION_mapping a
JOIN (
	SELECT
		t1.YYB,
		t1.khfs
	FROM
		TKHXX_mapping t1
	LEFT JOIN LCFXCKH_mapping t2 ON
		t1.khh = t2.gtkhh
	WHERE
		t1.khrq = ${RQ}
		AND t2.step in (5,6) AND t1.khzt <> 99
        ) c ON
	c.yyb = a.id
WHERE
	a.orgtype = 3
	AND a.id NOT IN (1052, 1058, 1088, 4444)
	GROUP BY a.id
    order by a.id;

create temporary view temp_xsyy_gfzjgwskhdxkhinfo as
select ${RQ} as rq,
  t.id as idx_id,
  a.yyb as yyb,
  t.idx_code as idx_code,
  cast(a.dxwcs as DECIMAL(20, 4)) as result
from temp_xsyy_gfzjgwskhdxkh a,TIC_ZBCS_mapping t where t.idx_code='XSYY_YYBWSKHDXWCS'
union all
select ${RQ} as rq,
  t.id as idx_id,
  a.yyb as yyb,
  t.idx_code as idx_code,
  cast(a.wskhwcs as DECIMAL(20, 4)) as result
from temp_xsyy_gfzjgwskhdxkh a,TIC_ZBCS_mapping t where t.idx_code='XSYY_YYBWSKHWCS'
union all
select ${RQ} as rq,
  t.id as idx_id,
  a.yyb as yyb,
  t.idx_code as idx_code,
  cast(a.dxkhzb as DECIMAL(20, 4)) as result
from temp_xsyy_gfzjgwskhdxkh a,TIC_ZBCS_mapping t where t.idx_code='XSYY_YYBWSKHDXKHZB';


upsert into TIC_YYBZB_mapping
select * from temp_xsyy_gfzjgwskhshs
union all
select * from temp_xsyy_gfzjgwskhdxkhinfo
;  


网上开户各人员见证审核情况：set RQ=${RQ};

  
--包含指标
--XSYY_SPRYWSKHJZS 各视频人员网上开户见证数
--XSYY_SHRYWSKHSHS 各审核人员网上开户审核数
--XSYY_SHRYWSKHFHS 各审核人员网上开户复核数



-- 各视频人员网上开户见证数
create temporary view temp_cif_gsprywskhjzs as 
select
    ${RQ} as rq,
    t.id as idx_id,
    t.idx_code as idx_code,
    a.result as `result`,
    a.yg as yg
from
    (  
        SELECT 
            tu.userid AS yg, coalesce(COUNT(lc.id), 0) RESULT
        FROM 
            app_odm_ods.tods_cif_tuser tu  
            left join LCFXCKH_mapping lc on lc.jzr = tu.id
        WHERE 
            lc.sqrq = ${RQ} and tu.status = 1 and tu.dt='${RQ}'
        GROUP BY 
            tu.userid, tu.NAME
    ) as a,
    TIC_ZBCS_mapping t
where
    t.idx_code = 'XSYY_SPRYWSKHJZS';

-- 各审核人员网上开户审核数
create temporary view temp_cif_gshrywskhshs as 
select
    ${RQ} as rq,
    t.id as idx_id,
    t.idx_code as idx_code,
    a.result as `result`,
    a.yg as yg
from
    (  
        SELECT 
            tu.userid AS yg, coalesce(COUNT(lc.id), 0) RESULT
        FROM 
            app_odm_ods.tods_cif_tuser tu 
            left join TYWQQ_CZLS_mapping lc
            on lc.czr = tu.id and (instr(lc.jdmc,'非现初审') > 0 or instr(lc.jdmc,'非现终审') > 0) and lc.czrq = ${RQ}
        WHERE 
            tu.id IN (
                SELECT 
                    s.userid
                FROM (
                    SELECT x.userid, x.roleid FROM app_odm_ods.tods_cif_lbmember x where x.dt='${RQ}'
                    UNION ALL
                    SELECT y.userid, y.roleid FROM app_odm_ods.tods_cif_lbmemberTEMP y where y.dt='${RQ}'
                ) s
                WHERE 
                    s.roleid = 202
            ) 
            and tu.status = 1 
            and tu.dt='${RQ}'
        GROUP BY tu.userid, tu.NAME
    ) as a,
    TIC_ZBCS_mapping t
where
    t.idx_code = 'XSYY_SHRYWSKHSHS';

-- 各审核人员网上开户复核数
create temporary view temp_cif_gshrywskhfhs as 
select
    ${RQ} as rq,
    t.id as idx_id,
    t.idx_code as idx_code,
    a.result as `result`,
    a.yg as yg
from
    (  
        SELECT 
            tu.userid AS yg, coalesce(COUNT(lc.id), 0) RESULT
        FROM 
            app_odm_ods.tods_cif_tuser tu 
            left join TYWQQ_CZLS_mapping lc
            on lc.czr = tu.id and instr(lc.jdmc,'非现复审') > 0 and lc.czrq = ${RQ}
        WHERE 
            tu.id IN (
                SELECT 
                    s.userid
                FROM (
                    SELECT x.userid, x.roleid FROM app_odm_ods.tods_cif_lbmember x where x.dt='${RQ}'
                    UNION ALL
                    SELECT y.userid, y.roleid FROM app_odm_ods.tods_cif_lbmemberTEMP y where y.dt='${RQ}'
                ) s
                WHERE 
                    s.roleid = 800
            ) 
            and tu.status = 1
            and tu.dt='${RQ}' 
        GROUP BY tu.userid, tu.NAME
    ) as a,
    TIC_ZBCS_mapping t
where
    t.idx_code = 'XSYY_SHRYWSKHFHS';

upsert into TIC_YGZB_mapping
select * from temp_cif_gsprywskhjzs
union all
select * from temp_cif_gshrywskhshs
union all
select * from temp_cif_gshrywskhfhs
;   

网上开户/线上业务提交量：set RQ=${RQ};


--包含指标
--XSYY_WSKHTJL 网上开户提交量
--XSYY_XSYWTJL 线上业务提交量

create temporary view temp_xsyy_wskhtjl as 
select 
    ${RQ} as rq,
    b.id as idx_id,
    b.idx_code as idx_code,
    (
        select 
            sum(result)
        from 
            TIC_TYZDXZB_mapping 
        where 
            idx_code = 'XSYY_WSKHTJSHSD' and rq = ${RQ}
    ) as result
from 
    TIC_ZBCS_mapping b
where 
    b.idx_code = 'XSYY_WSKHTJL'
union all
select 
    ${RQ} as rq,
    b.id as idx_id,
    b.idx_code as idx_code,
    (
        select 
            sum(result)
        from 
            TIC_TYZDXZB_mapping 
        where 
            idx_code = 'XSYY_XSYWTJSHSJFB' and rq = ${RQ}
    ) as result
from 
    TIC_ZBCS_mapping b
where 
    b.idx_code = 'XSYY_XSYWTJL' 
;

upsert into TIC_YYZB_mapping
select * from temp_xsyy_wskhtjl;


网上开户单/双向视频统计：set RQ=${RQ};

--包含指标
--XSYY_DXSPKHWCS 单向视频开户完成数
--XSYY_SXSPKHWCS 双向视频开户完成数
--XSYY_SXSPZB 双向视频占比
--XSYY_DXSPCGS 单向视频成功率
--XSYY_SXSPCGS 双向视频成功率
    

-- 单向视频开户完成数
create temporary view temp_xsyy_dxspkhwcs as 
select 
    ${RQ} as rq,
    b.id as idx_id,
    b.idx_code as idx_code,
    (
        select 
            count(1) as result
        from 
            TKHXX_mapping t1
            left join LCFXCKH_mapping t2 on t1.khh = t2.gtkhh
        where 
            t1.khrq = ${RQ} and t1.khfs = 5 and t2.step in (5,6) and t1.khzt <> 99
    ) as result
from 
    TIC_ZBCS_mapping b
where 
    b.idx_code = 'XSYY_DXSPKHWCS'
;

-- 双向视频开户完成数
create temporary view temp_xsyy_sxspkhwcs as 
select 
    ${RQ} as rq,
    b.id as idx_id,
    b.idx_code as idx_code,
    (
        select 
            count(1) as result
        from 
            TKHXX_mapping t1
            left join LCFXCKH_mapping t2 on t1.khh = t2.gtkhh
        where 
            t1.khrq = ${RQ} and t1.khfs = 3 and t2.step in (5,6) and t1.khzt <> 99
        ) as result
from 
    TIC_ZBCS_mapping b
where 
    b.idx_code = 'XSYY_SXSPKHWCS'
;

-- 双向视频占比
create temporary view temp_xsyy_sxspzb as 
select 
    ${RQ} as rq,
    b.id as idx_id,
    b.idx_code as idx_code,
    (
        SELECT CASE
            WHEN y.total = 0 THEN 0
            ELSE round(x.dxsps / y.total,4)
            END AS RESULT
        FROM 
            (
                SELECT 
                    COUNT(*) dxsps
                FROM 
                    TKHXX_mapping t1
                    left join LCFXCKH_mapping t2 on t1.khh = t2.gtkhh
                WHERE 
                    t1.khrq = ${RQ} and t1.khfs in (3) and t2.step in (5,6) and t1.khzt <> 99
            ) x, 
            (
                SELECT 
                    COUNT(*) total
                FROM 
                    TKHXX_mapping t1
                    left join LCFXCKH_mapping t2 on t1.khh = t2.gtkhh
                WHERE 
                    t1.khrq = ${RQ} and t1.khfs in (3,5) and t2.step in (5,6) and t1.khzt <> 99
            ) y
    ) as result
from 
    TIC_ZBCS_mapping b
where 
    b.idx_code = 'XSYY_SXSPZB'
;

-- 单向视频成功率
create temporary view temp_xsyy_dxspcgl as 
select 
    ${RQ} as rq,
    b.id as idx_id,
    b.idx_code as idx_code,
    (
        SELECT CASE
            WHEN y.total = 0 THEN 0
            ELSE
            round(x.dxsps / y.total,4)
            END AS RESULT
        FROM 
            (
                SELECT COUNT(*) dxsps
                FROM  TKHXX_mapping t1
                    left join LCFXCKH_mapping t2 on t1.khh = t2.gtkhh
                WHERE 
                    t1.khrq = ${RQ} and t1.khfs in (5) and t2.step in (5,6) and t1.khzt <> 99
            ) x, 
            (
                select 
                    (lc1.num1 - lc2.num2) as total
                from
                (
                    select 
                        count(1) num1 
                    from 
                        (
                            select 
                                zjbh
                            FROM 
                                LCFXCKH_mapping
                            WHERE 
                                splx = 1 and sqrq = ${RQ} and step in (1,2,3,5,6,99) 
                            group by 
                                zjlb,zjbh,khqc
                        ) lcfxckh
                ) lc1,   
                (
                    select 
                        count(1) as num2
                    from
                        (
                            select 
                                zjbh
                            from
                                (
                                    select 
                                        b.zjlb,b.zjbh,b.khqc,b.splx,b.step
                                    from 
                                        LCFXCKH_mapping a,
                                        LCFXCKH_mapping b
                                    where 
                                        a.sqrq = ${RQ} and a.step in (1,2,3,5,6,99) and a.splx = 1
                                        and b.sqrq = ${RQ} and b.step in (1,2,3,5,6,99) and b.splx = 0
                                        and a.zjlb = b.zjlb and a.zjbh = b.zjbh and a.khqc = b.khqc 
                                ) c
                            where 
                                step <> 99
                            group by zjlb,zjbh,khqc
                        ) d
                ) lc2    
            ) y
    ) as result
from 
    TIC_ZBCS_mapping b
where 
    b.idx_code = 'XSYY_DXSPCGS'
;

-- 双向视频成功率
create temporary view temp_xsyy_sxspcgl as 
select 
    ${RQ} as rq,
    b.id as idx_id,
    b.idx_code as idx_code,
    (
        SELECT CASE
            WHEN y.total = 0 THEN 0
            ELSE
            round(x.dxsps / y.total,4)
            END AS RESULT
        FROM 
            (
                SELECT COUNT(*) dxsps
                FROM  TKHXX_mapping t1
                    left join LCFXCKH_mapping t2 on t1.khh = t2.gtkhh
                WHERE 
                    t1.khrq = ${RQ} and t1.khfs in (3) and t2.step in (5,6) and t1.khzt <> 99
            ) x, 
            (
                select 
                    (lc1.num1 - lc2.num2) as total
                from
                (
                    select 
                        count(1) num1 
                    from 
                        (
                            select 
                                zjbh
                            FROM 
                                LCFXCKH_mapping
                            WHERE 
                                splx = 0 and sqrq = ${RQ} and step in (1,2,3,5,6,99) 
                            group by 
                                zjlb,zjbh,khqc
                        ) lcfxckh
                ) lc1,   
                (
                    select 
                        count(1) as num2
                    from
                        (
                            select 
                                zjbh
                            from
                                (
                                    select 
                                        b.zjlb,b.zjbh,b.khqc,b.splx,b.step
                                    from 
                                        LCFXCKH_mapping a,
                                        LCFXCKH_mapping b
                                    where 
                                        a.sqrq = ${RQ} and a.step in (1,2,3,5,6,99) and a.splx = 0
                                        and b.sqrq = ${RQ} and b.step in (1,2,3,5,6,99) and b.splx = 1
                                        and a.zjlb = b.zjlb and a.zjbh = b.zjbh and a.khqc = b.khqc 
                                ) c
                            where 
                                step <> 99
                            group by zjlb,zjbh,khqc
                        ) d
                ) lc2    
            ) y
    ) as result
from 
    TIC_ZBCS_mapping b
where 
    b.idx_code = 'XSYY_SXSPCGS'
;

upsert into TIC_YYZB_mapping
create temporary view temp_xsyy_yyzb as 
select * from temp_xsyy_dxspkhwcs
union all
select * from temp_xsyy_sxspkhwcs
union all
select * from temp_xsyy_sxspzb
union all
select * from temp_xsyy_dxspcgl
union all
select * from temp_xsyy_sxspcgl
;

网上开户各分支机构本年累计开户统计：set RQ=${RQ};

--包含指标
--XSYY_YYBWSKHBNLJKHWCS 各分支机构网上开户本年累计开户完成数top5

create temporary view temp_xsyy_gfzjgwskhbnljkhwcs as 
select
  ${RQ} as rq,
  t.id as idx_id,
  a.yyb as yyb,
  t.idx_code as idx_code,
  a.result as result
from
  (
    select
      a.id as yyb,
      COUNT(c.yyb) AS result
    from
      LBORGANIZATION_mapping a
      left join TXTDM_mapping b on a.orgtype = b.ibm
      left join TKHXX_mapping c on a.id = c.yyb 
    where
      b.fldm = 'LB.ORGANIZATION'
      and c.khfs in (3,5)  
      and c.khrq <= ${RQ} 
      and c.khrq >= concat(substr('${RQ}',1,4),'0101')
      and (c.khzt = 0 or (c.khzt = 3 and c.xhrq is not null))
      and a.orgtype = 3
    GROUP BY
      a.id
  ) as a,
  TIC_ZBCS_mapping t
where
  t.idx_code = 'XSYY_YYBWSKHBNLJKHWCS';


upsert into TIC_YYBZB_mapping
select * from temp_xsyy_gfzjgwskhbnljkhwcs
;


网上开户各审核人员审核/复核不通过数统计：set RQ=${RQ};
set dt=${dt};


--包含指标
--XSYY_SHRYWSKHSHBTGS 各审核人员网上开户审核不通过数
--XSYY_SHRYWSKHFHBTGS 各审核人员网上开户复核不通过数

-- 各审核人员网上开户审核不通过数
create temporary view temp_cif_gshrywskhshbtgs as 
SELECT 
    tu.userid AS yg, coalesce(COUNT(ls.id), 0) RESULT
FROM 
    app_odm_ods.tods_cif_tuser tu
    left join TYWQQ_CZLS_mapping ls
    on ls.czr=tu.id and ls.czrq=${RQ} and (jdmc = '非现初审终止办理' or jdmc = '非现终审终止办理')
WHERE 
    tu.id IN (
        SELECT s.userid
        FROM 
            (
                SELECT x.userid, x.roleid FROM app_odm_ods.tods_cif_lbmember x where x.dt='${dt}'
                UNION ALL
                SELECT y.userid, y.roleid FROM app_odm_ods.tods_cif_lbmemberTEMP y where y.dt='${dt}'
            ) s
        WHERE s.roleid = 202
    ) 
    and tu.dt='${dt}'
GROUP BY tu.userid, tu.NAME
;

-- 各审核人员网上开户复核不通过数
create temporary view temp_cif_gshrywskhfhbtgs as 
SELECT 
    tu.userid AS yg, coalesce(COUNT(ls.id), 0) RESULT
FROM 
    app_odm_ods.tods_cif_tuser tu
    left join TYWQQ_CZLS_mapping ls
    on ls.czr=tu.id and ls.czrq=${RQ} and ls.jdmc='非现复审终止办理'
WHERE 
    tu.id IN (
        SELECT s.userid
        FROM 
            (
                SELECT x.userid, x.roleid FROM app_odm_ods.tods_cif_lbmember x where x.dt='${dt}'
                UNION ALL
                SELECT y.userid, y.roleid FROM app_odm_ods.tods_cif_lbmemberTEMP y where y.dt='${dt}'
            ) s
        WHERE s.roleid = 800
    ) 
    and tu.dt='${dt}'
GROUP BY tu.userid, tu.NAME
;


upsert into TIC_YGZB_mapping
select 
    ${RQ} as rq,
    b.id as idx_id,
    a.yg as yg, 
    b.idx_code as idx_code,
    a.result as result 
from 
    temp_cif_gshrywskhshbtgs a,
    TIC_ZBCS_mapping  b 
where b.idx_code = 'XSYY_SHRYWSKHSHBTGS'
union all
select 
    ${RQ} as rq,
    b.id as idx_id,
    a.yg as yg, 
    b.idx_code as idx_code,
    a.result as result 
from 
    temp_cif_gshrywskhfhbtgs a,
    TIC_ZBCS_mapping  b 
where b.idx_code = 'XSYY_SHRYWSKHFHBTGS'
;

网上开户年/月度网上开户成功率：set RQ=${RQ};  

--包含指标
--XSYY_JRWSKHCGL_M 月度网上开户成功率
--XSYY_JRWSKHCGL_Y 年度网上开户成功率

-- 月度网上开户成功率
create temporary view temp_xsyy_ydwskhcgl as 
select
  ${RQ} as rq,
  t.id as idx_id,
  t.idx_code  as idx_code,
  a.result as result
from
  (
    SELECT 
        CASE
            WHEN w.tjs = 0 THEN 0
            ELSE round(x.cgs / w.tjs,4)
        END AS RESULT
    FROM 
        (
            SELECT 
                COUNT(*) cgs
            FROM 
                TKHXX_mapping t1
                left join LCFXCKH_mapping t2 on t1.khh = t2.gtkhh
            WHERE 
                t1.KHRQ>=cast(FROM_UNIXTIME(UNIX_TIMESTAMP(CAST(${RQ} AS STRING),'yyyyMMdd')-30*24*60*60,'yyyyMMdd') as int) AND t1.KHRQ<=${RQ}
                AND t1.khfs IN (3, 5) and t2.step in (5,6) and t1.khzt <> 99
        ) x ,   
        (
            select count(*) tjs from (
                select zjbh
                FROM LCFXCKH_mapping
                WHERE SQRQ>=cast(FROM_UNIXTIME(UNIX_TIMESTAMP(CAST(${RQ} AS STRING),'yyyyMMdd')-30*24*60*60,'yyyyMMdd') as int)  AND SQRQ<=${RQ} and step in (1,2,3,5,6,99) 
                group by zjlb,zjbh,khqc
            ) x
        ) w
  ) as a,
  TIC_ZBCS_mapping t
where
  t.idx_code = 'XSYY_JRWSKHCGL_M';

-- 年度网上开户成功率
create temporary view temp_xsyy_ndwskhcgl as 
select
  ${RQ} as rq,
  t.id as idx_id,
  t.idx_code  as idx_code,
  a.result as result
from
  (SELECT CASE
           WHEN w.tjs = 0 THEN 0
           ELSE
            round(x.cgs / w.tjs,4)
       END AS RESULT
  FROM (SELECT COUNT(*) cgs
           FROM 
            TKHXX_mapping t1
            left join LCFXCKH_mapping t2 on t1.khh = t2.gtkhh
          WHERE t1.KHRQ>=cast(CONCAT(CAST(SUBSTRING(CAST(${RQ} AS STRING), 1, 4) AS STRING), '0101') as int) 
            AND t1.KHRQ<=${RQ}
            AND t1.khfs IN (3, 5) and t2.step in (5,6) and t1.khzt <> 99) x ,   
        (select count(*) tjs from (
        select zjbh
           FROM LCFXCKH_mapping
          WHERE SQRQ>=cast(CONCAT(CAST(SUBSTRING(CAST(${RQ} AS STRING), 1, 4) AS STRING), '0101') as int)  
           and sqrq<= ${RQ}
          and step in (1,2,3,5,6,99) 
          group by zjlb,zjbh,khqc) x) w
  ) as a,
  TIC_ZBCS_mapping t
where
  t.idx_code = 'XSYY_JRWSKHCGL_Y';

upsert into TIC_YYZB_mapping
select * from temp_xsyy_ydwskhcgl
union all
select * from temp_xsyy_ndwskhcgl
;


