线上业务耗时脚本：set RQ=${RQ};
set dt=${dt};

--包含指标
--XSYY_RJSHSC 人均审核时长
--XSYY_SHRYXSYWSHSC 各审核人员线上业务审核时长
--XSYY_SHRYXSYWPJMBBLSC 各审核人员线上业务平均每笔办理时长
--XSYY_SHRYXSYWPJMBDHSC 各审核人员线上业务平均每笔等候时长
--XSYY_SHRYXSYWPJMBSHSC 各审核人员线上业务平均每笔审核时长

--数据清洗 线上业务审核明细
create temporary view xsywshl as 
select tq.id as ywqqid
from TYWQQ_CZLS_mapping b
       right join TYWQQ_mapping tq on b.ywqqid = tq.id
        left join TXTDM_mapping t3 on tq.ywdm = t3.cbm and t3.fldm='CIF_YWDM'
where 1 = 1
  and tq.blms = 2
  and tq.ywdm <> '00003'
  and ((b.czrq = ${RQ}
    and b.czlx in (301, 302)
    and b.czr in (SELECT lb.userid
                FROM app_odm_ods.tods_cif_lbmember lb
                WHERE lb.roleid IN (809) and lb.dt='${dt}'
                UNION 
                SELECT lbtemp.userid
                FROM app_odm_ods.tods_cif_lbmemberTEMP lbtemp
                WHERE lbtemp.roleid IN (809) and lbtemp.dt='${dt}')
           )
    or (tq.jzr is not null and tq.gllc is null and tq.sqrq = ${RQ} and (tq.ywdm <> '00013' or 
                                                                        tq.ywdm = '00013' and tq.clzt in (33,34,99))
        ) --见证无审核。网上销户，当天存在33待办理或者34待办理已认领
       );

--线上业务人员
create temporary view xsywry as 
SELECT id,userid,name 
FROM app_odm_ods.tods_cif_tuser 
WHERE id IN (SELECT s.userid
             FROM (SELECT x.userid, x.roleid
                   FROM app_odm_ods.tods_cif_lbmemberTEMP x
                   where x.dt='${dt}'
                   UNION ALL
                   SELECT y.userid, y.roleid 
                   FROM app_odm_ods.tods_cif_lbmember y
                   where y.dt='${dt}'
                  ) s
             WHERE s.roleid = 809)
       and dt='${dt}' ;

--各审核人员线上业务平均每笔等候时长
CREATE TEMPORARY VIEW t_dwd_dh AS
 SELECT id,ywqqid,czsj,czr,czlx,czrq
    FROM (
        SELECT *,
               LAG(CZLX) OVER (PARTITION BY YWQQID ORDER BY ID) as prev_czlx,
               LEAD(CZLX) OVER (PARTITION BY YWQQID ORDER BY ID) as next_czlx
        FROM TYWQQ_CZLS_mapping a
        WHERE CZLX IN (180, 321,301)
        and ywqqid in (select ywqqid from xsywshl)
    ) t
   WHERE (CZLX = 180 AND next_czlx = 321) 
        OR (CZLX = 321 AND (next_czlx <> 321 or next_czlx is null) AND prev_czlx IS NOT null)
        OR (czlx=301 AND next_czlx=321) 
   ;

CREATE TEMPORARY VIEW dhsc_temp AS
select  ywqqid,czr,sum(UNIX_TIMESTAMP(concat(endrq,' ',endtime), 'yyyyMMdd HH:mm:ss') - UNIX_TIMESTAMP(concat(startrq,' ',starttime), 'yyyyMMdd HH:mm:ss')) AS dhsc 
from (SELECT 
  t1.ywqqid AS ywqqid,t2.czr AS czr,t1.czsj AS starttime, t1.czrq AS startrq,t2.czsj AS endtime,t2.czrq AS endrq, t1.czlx AS czlx1, t2.czlx AS czlx2
FROM 
  (SELECT *, ROW_NUMBER() OVER(PARTITION BY YWQQID ORDER BY id) as row_num FROM t_dwd_dh) t1
JOIN 
  (SELECT *, ROW_NUMBER() OVER(PARTITION BY YWQQID ORDER BY id) as row_num FROM t_dwd_dh) t2
ON 
  t1.row_num = t2.row_num - 1 AND t1.row_num % 2 = 1 and t1.ywqqid=t2.ywqqid)
group by ywqqid,czr;


create temporary view dhsc as 
select a.userid,nvl(b.pjsc,0) pjsc,'XSYY_SHRYXSYWPJMBDHSC' as idx_code from xsywry a left join(
    select round(avg(dhsc)/60,2) as pjsc,czr from dhsc_temp d group by czr) b
on a.id=b.czr;

--各审核人员线上业务平均每笔审核时长
create temporary view t_dwd_sh as
 SELECT *,
    ROW_NUMBER() OVER(PARTITION BY YWQQID ORDER BY CONCAT(CZRQ,' ',CZSJ)) as row_num
    FROM TYWQQ_CZLS_mapping a
    WHERE CZLX IN (301, 302, 321)
    and ywqqid in (select ywqqid from xsywshl);

create temporary view shsc_temp as
select  ywqqid,czr,sum(sjc) AS shsc 
from (
    SELECT 
        start.YWQQID, 
        start.ID AS start_id, 
        start.CZSJ AS start_time, 
        end.CZSJ AS end_time, 
        end.czr AS czr,
        UNIX_TIMESTAMP(CONCAT(end.CZRQ,' ',end.CZSJ), 'yyyyMMdd HH:mm:ss') - UNIX_TIMESTAMP(CONCAT(start.CZRQ,' ',start.CZSJ), 'yyyyMMdd HH:mm:ss') AS sjc
    FROM 
        (SELECT * FROM t_dwd_sh WHERE CZLX = 321) start
    JOIN 
        (SELECT * FROM t_dwd_sh WHERE CZLX IN (301, 302)) end
    ON 
        start.YWQQID = end.YWQQID AND start.row_num + 1 = end.row_num
    ORDER BY 
        start.YWQQID, start_time
)
group by ywqqid,czr;

--平均每笔审核时长
create temporary view shsc as
select a.userid,nvl(b.pjsc,0) pjsc,'XSYY_SHRYXSYWPJMBSHSC' as idx_code from xsywry a left join(
    select round(avg(shsc)/60,2) as pjsc,czr from shsc_temp d group by czr) b
on a.id=b.czr;

--各审核人员线上业务审核时长
create temporary view zshsc as
select a.userid,nvl(b.pjsc,0) pjsc,'XSYY_SHRYXSYWSHSC' as idx_code from xsywry a left join(
    select round(sum(shsc)/60,2) as pjsc,czr from shsc_temp d group by czr) b
on a.id=b.czr;

--人均审核时长
create temporary view rjshsc as
select COALESCE(round(avg(w.zshsc)/60,2), 0) as pjsc from (
select sum(shsc) as zshsc,czr from shsc_temp d group by czr) w;

--各审核人员线上业务平均每笔办理时长
create temporary view blsc_temp as 
select ywqqid,czr,sum(sc) as sc
from (
    select ywqqid,czr,shsc as sc from shsc_temp
    union all
    select ywqqid,czr,dhsc as sc from dhsc_temp
) a
group by ywqqid,czr;


create temporary view blsc as
select a.userid,nvl(b.pjsc,0) pjsc,'XSYY_SHRYXSYWPJMBBLSC' as idx_code from xsywry a left join(
    select round(avg(sc)/60,2) as pjsc,czr from blsc_temp d group by czr) b
on a.id=b.czr;


upsert into TIC_YXZB_mapping
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.pjsc as result
from
  TIC_ZBCS_mapping a,rjshsc b where a.idx_code = 'XSYY_RJSHSC';

upsert into TIC_YGZB_mapping
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.userid as yg, 
  b.pjsc as result
from
  TIC_ZBCS_mapping a,zshsc b where a.idx_code = 'XSYY_SHRYXSYWSHSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.userid as yg, 
  b.pjsc as result
from
  TIC_ZBCS_mapping a,blsc b where a.idx_code = 'XSYY_SHRYXSYWPJMBBLSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.userid as yg, 
  b.pjsc as result
from
  TIC_ZBCS_mapping a,dhsc b where a.idx_code = 'XSYY_SHRYXSYWPJMBDHSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.userid as yg, 
  b.pjsc as result
from
  TIC_ZBCS_mapping a,shsc b where a.idx_code = 'XSYY_SHRYXSYWPJMBSHSC';
  

柜面业务耗时脚本：set RQ = ${RQ};
set dt = ${dt};

create temporary view fhry as
select 
    id,userid,name,orgid
from 
    app_odm_ods.tods_cif_tuser
where 
    orgid not in (1052,1058,1088,4444) 
    and status = 1 
    and id in (SELECT s.userid
                FROM (SELECT x.userid, x.roleid FROM  app_odm_ods.tods_cif_lbmember x where x.dt='${RQ}'
                      UNION ALL
                      SELECT y.userid, y.roleid FROM app_odm_ods.tods_cif_lbmemberTEMP y where y.dt='${RQ}'
                    ) s
                WHERE s.roleid in (60,813)
    )
    and dt='${RQ}';

create temporary view yybry as
SELECT id AS czr FROM app_odm_ods.tods_cif_tuser a 
WHERE EXISTS (SELECT 1 FROM LBORGANIZATION_mapping b WHERE a.ORGID =b.id and b.ORGTYPE=3)
     and a.dt='${RQ}';

--初步清洗，选定范围
create temporary view t_total as
SELECT 
	 ywqqid,czlx,czr,flag
FROM (
	SELECT a.YWQQID    as YWQQID
	     , a.CZLX      as CZLX
         , min(a.CZR)  as CZR
	     , min(b.CLZT) as CLZT
	     , min(b.ZJLB) as ZJLB
	FROM 
	    TYWQQ_CZLS_mapping a
	    LEFT JOIN TYWQQ_mapping b ON a.ywqqid = b.ID
	WHERE 
	    a.CZRQ = ${RQ}
	    AND a.CZLX IN (211, 301, 302, 311) --业务退回发起人、审批通过、审批不通过、审批退回
	    AND b.CLZT in ('42', '8', '9', '80') --审核不通过、终止、成功、失败、部分成功
	    AND a.czr IN (select id from fhry )
        AND b.ywdm <> '00003'
	group by a.YWQQID, a.CZLX) temp1
LEFT JOIN (select * from TXTDM_mapping where fldm = 'GT_ZJLB') t3 on temp1.zjlb = t3.ibm;


create temporary view ythid as
select distinct ywqqid as ywqqid from t_total where czlx in (211,311);

create temporary view wthid as
select ywqqid from t_total where ywqqid not in (select ywqqid from ythid);

create temporary view grid as
select ywqqid from t_total where flag=1;

create temporary view jgid as
select ywqqid from t_total where flag=2;


-- -------------------------------------------------------------------------------------------------------------
--个人复核时长
create temporary view t_dwd_grfh as
 SELECT *,
    ROW_NUMBER() OVER(PARTITION BY YWQQID ORDER BY CONCAT(CZRQ,' ',CZSJ)) as row_num
    FROM TYWQQ_CZLS_mapping a
    WHERE CZLX IN (301, 302, 211, 311, 321)
    and ywqqid in (select ywqqid from grid);

create temporary view grfhsc_temp as
select  ywqqid,czr,sum(sjc) AS shsc 
from (
    SELECT 
        start.YWQQID, 
        start.ID AS start_id, 
        start.CZSJ AS start_time, 
        end.CZSJ AS end_time, 
        end.czr AS czr,
        UNIX_TIMESTAMP(CONCAT(end.CZRQ,' ',end.CZSJ), 'yyyyMMdd HH:mm:ss') - UNIX_TIMESTAMP(CONCAT(start.CZRQ,' ',start.CZSJ), 'yyyyMMdd HH:mm:ss') AS sjc
    FROM 
        (SELECT * FROM t_dwd_grfh WHERE CZLX = 321) start
    JOIN 
        (SELECT * FROM t_dwd_grfh WHERE CZLX IN (301, 302, 211, 311)) end
    ON 
        start.YWQQID = end.YWQQID AND start.row_num + 1 = end.row_num
    ORDER BY 
        start.YWQQID, start_time
)
group by ywqqid,czr;


create temporary view grfhsc as
select a.name,nvl(b.pjsc,0) pjsc, 'JZYY_FHGYGRYWSC' as idx_code  from fhry a left join(
    select round(avg(shsc)/60,2) as pjsc,czr from grfhsc_temp d group by czr) b
on a.id=b.czr;

---------------------------------------------------------------------------------------

--机构复核时长
create temporary view t_dwd_jgfh as
 SELECT *,
    ROW_NUMBER() OVER(PARTITION BY YWQQID ORDER BY CONCAT(CZRQ,' ',CZSJ)) as row_num
    FROM TYWQQ_CZLS_mapping a
    WHERE CZLX IN (301, 302, 211, 311, 321)
    and ywqqid in (select ywqqid from jgid);

create temporary view jgfhsc_temp as
select  ywqqid,czr,sum(sjc) AS shsc 
from (
    SELECT 
        start.YWQQID, 
        start.ID AS start_id, 
        start.CZSJ AS start_time, 
        end.CZSJ AS end_time, 
        end.czr AS czr,
        UNIX_TIMESTAMP(CONCAT(end.CZRQ,' ',end.CZSJ), 'yyyyMMdd HH:mm:ss') - UNIX_TIMESTAMP(CONCAT(start.CZRQ,' ',start.CZSJ), 'yyyyMMdd HH:mm:ss') AS sjc
    FROM 
        (SELECT * FROM t_dwd_jgfh WHERE CZLX = 321) start
    JOIN 
        (SELECT * FROM t_dwd_jgfh WHERE CZLX IN (301, 302, 211, 311)) end
    ON 
        start.YWQQID = end.YWQQID AND start.row_num + 1 = end.row_num
    ORDER BY 
        start.YWQQID, start_time
)
group by ywqqid,czr;


create temporary view jgfhsc as
select a.name,nvl(b.pjsc,0) pjsc, 'JZYY_FHGYJGYWSC' as idx_code  from fhry a left join(
    select round(avg(shsc)/60,2) as pjsc,czr from jgfhsc_temp d group by czr) b
on a.id=b.czr;

------------------------------------------------------------------------------------------
--人均个人复核时长
create temporary view rjgrfhsc as 
select round(avg(b.pjsc),2) as pjsc from (
    select avg(shsc)/60 as pjsc,czr from grfhsc_temp d where czr in (select id from fhry) group by czr) b;

--人均机构复核时长
create temporary view rjjgfhsc as 
select round(avg(b.pjsc),2) as pjsc from (
    select avg(shsc)/60 as pjsc,czr from jgfhsc_temp d where czr in (select id from fhry) group by czr) b;


--------------------------------------------------------------------------------------
--无退回审核时长
create temporary view t_dwd_wthsh as
 SELECT *,
    ROW_NUMBER() OVER(PARTITION BY YWQQID ORDER BY CONCAT(CZRQ,' ',CZSJ)) as row_num
    FROM TYWQQ_CZLS_mapping a
    WHERE CZLX IN (301, 302, 211, 311, 321)
    and ywqqid in (select ywqqid from wthid);

create temporary view wthshsc_temp as
select  ywqqid,czr,sum(sjc) AS shsc 
from (
    SELECT 
        start.YWQQID, 
        start.ID AS start_id, 
        start.CZSJ AS start_time, 
        end.CZSJ AS end_time, 
        end.czr AS czr,
        UNIX_TIMESTAMP(CONCAT(end.CZRQ,' ',end.CZSJ), 'yyyyMMdd HH:mm:ss') - UNIX_TIMESTAMP(CONCAT(start.CZRQ,' ',start.CZSJ), 'yyyyMMdd HH:mm:ss') AS sjc
    FROM 
        (SELECT * FROM t_dwd_wthsh WHERE CZLX = 321) start
    JOIN 
        (SELECT * FROM t_dwd_wthsh WHERE CZLX IN (301, 302, 211, 311)) end
    ON 
        start.YWQQID = end.YWQQID AND start.row_num + 1 = end.row_num
    ORDER BY 
        start.YWQQID, start_time
)
group by ywqqid,czr;


create temporary view wthshsc as
select a.name,nvl(b.pjsc,0) pjsc,'JZYY_FHRYWTHPJMBSHSC' as idx_code from fhry a left join(
    select round(avg(shsc)/60,2) as pjsc,czr from wthshsc_temp d group by czr) b
on a.id=b.czr;



-------------------------------------------------------------------

--有退回审核时长
create temporary view t_dwd_ythsh as
 SELECT *,
    ROW_NUMBER() OVER(PARTITION BY YWQQID ORDER BY CONCAT(CZRQ,' ',CZSJ)) as row_num
    FROM TYWQQ_CZLS_mapping a
    WHERE CZLX IN (301, 302, 211, 311, 321)
    and ywqqid in (select ywqqid from ythid);

create temporary view ythshsc_temp as
select  ywqqid,czr,sum(sjc) AS shsc 
from (
    SELECT 
        start.YWQQID, 
        start.ID AS start_id, 
        start.CZSJ AS start_time, 
        end.CZSJ AS end_time, 
        end.czr AS czr,
        UNIX_TIMESTAMP(CONCAT(end.CZRQ,' ',end.CZSJ), 'yyyyMMdd HH:mm:ss') - UNIX_TIMESTAMP(CONCAT(start.CZRQ,' ',start.CZSJ), 'yyyyMMdd HH:mm:ss') AS sjc
    FROM 
        (SELECT * FROM t_dwd_ythsh WHERE CZLX = 321) start
    JOIN 
        (SELECT * FROM t_dwd_ythsh WHERE CZLX IN (301, 302, 211, 311)) end
    ON 
        start.YWQQID = end.YWQQID AND start.row_num + 1 = end.row_num
    ORDER BY 
        start.YWQQID, start_time
)
group by ywqqid,czr;


create temporary view ythshsc as
select a.name,nvl(b.pjsc,0) pjsc,'JZYY_FHRYYTHPJMBSHSC' as idx_code from fhry a left join(
    select round(avg(shsc)/60,2) as pjsc,czr from ythshsc_temp d group by czr) b
on a.id=b.czr;

-----------------------------------------------------------------------
--有退回等候时长
CREATE TEMPORARY VIEW t_dwd_ythdh AS
 SELECT id,ywqqid,czsj,czr,czlx,czrq
    FROM (
        SELECT *,
               LAG(CZLX) OVER (PARTITION BY YWQQID ORDER BY ID) as prev_czlx,
               LEAD(CZLX) OVER (PARTITION BY YWQQID ORDER BY ID) as next_czlx
        FROM TYWQQ_CZLS_mapping a
        WHERE CZLX IN (180, 321,301)
        and ywqqid in (select ywqqid from ythid)
    ) t
   WHERE (CZLX = 180 AND next_czlx = 321) 
        OR (CZLX = 321 AND (next_czlx <> 321 or next_czlx is null) AND prev_czlx IS NOT null)
        OR (czlx=301 AND next_czlx=321);


CREATE TEMPORARY VIEW ythdhsc_temp AS
select  ywqqid,czr,sum(UNIX_TIMESTAMP(concat(endrq,' ',endtime), 'yyyyMMdd HH:mm:ss') - UNIX_TIMESTAMP(concat(startrq,' ',starttime), 'yyyyMMdd HH:mm:ss')) AS dhsc 
from (SELECT 
  t1.ywqqid AS ywqqid,t2.czr AS czr,t1.czsj AS starttime, t1.czrq AS startrq,t2.czsj AS endtime,t2.czrq AS endrq, t1.czlx AS czlx1, t2.czlx AS czlx2
FROM 
  (SELECT *, ROW_NUMBER() OVER(PARTITION BY YWQQID ORDER BY id) as row_num FROM t_dwd_ythdh) t1
JOIN 
  (SELECT *, ROW_NUMBER() OVER(PARTITION BY YWQQID ORDER BY id) as row_num FROM t_dwd_ythdh) t2
ON 
  t1.row_num = t2.row_num - 1 AND t1.row_num % 2 = 1 and t1.ywqqid=t2.ywqqid)
group by ywqqid,czr;


create temporary view ythdhsc as
select a.name,nvl(b.pjsc,0) pjsc,'JZYY_FHRYYTHPJMBDHSC' as idx_code from fhry a left join(
    select round(avg(dhsc)/60,2) as pjsc,czr from ythdhsc_temp d group by czr) b
on a.id=b.czr;

-----------------------------------------------------------------------
--无退回等候时长
CREATE TEMPORARY VIEW t_dwd_wthdh AS
SELECT id,ywqqid,czsj,czr,czlx,czrq
    FROM (
        SELECT *,
               LAG(CZLX) OVER (PARTITION BY YWQQID ORDER BY ID) as prev_czlx,
               LEAD(CZLX) OVER (PARTITION BY YWQQID ORDER BY ID) as next_czlx
        FROM TYWQQ_CZLS_mapping a
        WHERE CZLX IN (180, 321,301)
        and ywqqid in (select ywqqid from wthid)
    ) t
   WHERE (CZLX = 180 AND next_czlx = 321) 
        OR (CZLX = 321 AND (next_czlx <> 321 or next_czlx is null) AND prev_czlx IS NOT null)
        OR (czlx=301 AND next_czlx=321);

CREATE TEMPORARY VIEW wthdhsc_temp AS
    select  ywqqid,czr,sum(UNIX_TIMESTAMP(concat(endrq,' ',endtime), 'yyyyMMdd HH:mm:ss') - UNIX_TIMESTAMP(concat(startrq,' ',starttime), 'yyyyMMdd HH:mm:ss')) AS dhsc 
    from (SELECT 
    t1.ywqqid AS ywqqid,t2.czr AS czr,t1.czsj AS starttime, t1.czrq AS startrq,t2.czsj AS endtime,t2.czrq AS endrq, t1.czlx AS czlx1, t2.czlx AS czlx2
    FROM 
    (SELECT *, ROW_NUMBER() OVER(PARTITION BY YWQQID ORDER BY id) as row_num FROM t_dwd_wthdh) t1
    JOIN 
    (SELECT *, ROW_NUMBER() OVER(PARTITION BY YWQQID ORDER BY id) as row_num FROM t_dwd_wthdh) t2
    ON 
    t1.row_num = t2.row_num - 1 AND t1.row_num % 2 = 1 and t1.ywqqid=t2.ywqqid)
    group by ywqqid,czr;


create temporary view wthdhsc as
select a.name,nvl(b.pjsc,0) pjsc,'JZYY_FHRYWTHPJMBDHSC' as idx_code from fhry a left join(
    select round(avg(dhsc)/60,2) as pjsc,czr from wthdhsc_temp d group by czr) b
on a.id=b.czr;

-----------------------------------------
--有/无退回办理时长
create temporary view wthblsc_temp as 
select ywqqid,czr,sum(sc) as sc
from (
    select ywqqid,czr,shsc as sc from wthshsc_temp
    union all
    select ywqqid,czr,dhsc as sc from wthdhsc_temp
) a
group by ywqqid,czr;


create temporary view wthblsc as
select a.name,nvl(b.pjsc,0) pjsc,'JZYY_FHRYWTHPJMBBLSC' as idx_code from fhry a left join(
    select round(avg(sc)/60,2) as pjsc,czr from wthblsc_temp d group by czr) b
on a.id=b.czr;



create temporary view ythblsc_temp as 
select ywqqid,czr,sum(sc) as sc
from (
    select ywqqid,czr,shsc as sc from ythshsc_temp
    union all
    select ywqqid,czr,dhsc as sc from ythdhsc_temp
) a
group by ywqqid,czr;


create temporary view ythblsc as
select a.name,nvl(b.pjsc,0) pjsc,'JZYY_FHRYYTHPJMBBLSC' as idx_code from fhry a left join(
    select round(avg(sc)/60,2) as pjsc,czr from ythblsc_temp d group by czr) b
on a.id=b.czr;

-- --无退回平均每笔办理时长(用于预计耗时计算)
create temporary view wthzblsc as
select round(avg(sc)/60,2) as pjsc from (
select sum(sc) as sc from wthblsc_temp where czr in (select id from fhry) group by ywqqid);

-----------------------------------------------
--总等候时长
CREATE TEMPORARY VIEW t_dwd_zdh AS
SELECT id,ywqqid,czsj,czr,czlx,czrq
    FROM (
        SELECT *,
               LAG(CZLX) OVER (PARTITION BY YWQQID ORDER BY ID) as prev_czlx,
               LEAD(CZLX) OVER (PARTITION BY YWQQID ORDER BY ID) as next_czlx
        FROM TYWQQ_CZLS_mapping a
        WHERE CZLX IN (180, 321,301)
        and ywqqid in (select ywqqid from t_total)
    ) t
   WHERE (CZLX = 180 AND next_czlx = 321) 
        OR (CZLX = 321 AND (next_czlx <> 321 or next_czlx is null) AND prev_czlx IS NOT null)
        OR (czlx=301 AND next_czlx=321);


CREATE TEMPORARY VIEW zdhsc_temp AS
select  ywqqid,sum(UNIX_TIMESTAMP(concat(endrq,' ',endtime), 'yyyyMMdd HH:mm:ss') - UNIX_TIMESTAMP(concat(startrq,' ',starttime), 'yyyyMMdd HH:mm:ss')) AS dhsc 
from (SELECT 
  t1.ywqqid AS ywqqid,t2.czr AS czr,t1.czsj AS starttime, t1.czrq AS startrq,t2.czsj AS endtime,t2.czrq AS endrq, t1.czlx AS czlx1, t2.czlx AS czlx2
FROM 
  (SELECT *, ROW_NUMBER() OVER(PARTITION BY YWQQID ORDER BY id) as row_num FROM t_dwd_zdh) t1
JOIN 
  (SELECT *, ROW_NUMBER() OVER(PARTITION BY YWQQID ORDER BY id) as row_num FROM t_dwd_zdh) t2
ON 
  t1.row_num = t2.row_num - 1 AND t1.row_num % 2 = 1 and t1.ywqqid=t2.ywqqid) 
where czr in (select id from fhry)
group by ywqqid;


create temporary view zdhsc as
select round(avg(dhsc)/60,2) as pjsc from zdhsc_temp;

-----------------------------------------------
--无退回营业部办理时长
create temporary view wthyybblsc_temp as 
select ywqqid,czr,sum(sjc) as sc
from (
    SELECT w.*,coalesce(fhry.id ,w.caller) AS czr FROM (
		SELECT 
            a2.ywqqid,a3.entry_id,
            UNIX_TIMESTAMP(a3.finish_date, 'yyyyMMdd HH:mm:ss')-UNIX_TIMESTAMP(a3.start_date, 'yyyyMMdd HH:mm:ss') as sjc,
            a3.start_date,
            a3.finish_date,
            a3.caller
            ,ROW_NUMBER() over(PARTITION BY ENTRY_ID ORDER BY caller) AS row_num
		FROM  (
			SELECT a.ywqqid,a.instid,b.state 
			FROM LCGTYW_mapping a 
			LEFT JOIN OS_WFENTRY_mapping b ON a.INSTID =b.ID  
			WHERE a.INSTID <> -1
			UNION 
			SELECT a.ywqqid,a.INSTID,b.state 
			FROM LCGTYW_KHXXXG_mapping a
			LEFT JOIN OS_WFENTRY_mapping b ON a.INSTID =b.id 
			WHERE a.INSTID <> -1
		) a2 
		LEFT JOIN (
			SELECT entry_id,min(start_date) AS start_date,max(finish_date) AS finish_date,caller FROM OS_HISTORYSTEP_mapping WHERE caller IS NOT NULL GROUP BY ENTRY_ID,caller 
		) a3
        ON a2.instid =a3.entry_id
        WHERE  a2.ywqqid IN (select ywqqid from wthid)
            AND a3.caller IN (SELECT czr FROM yybry)
            AND a3.start_date <> a3.finish_date	 
    ) w 
    LEFT JOIN 
    (SELECT ENTRY_ID,caller,ROW_NUMBER() over(PARTITION BY ENTRY_ID ORDER BY caller) AS row_num 
        FROM OS_HISTORYSTEP_mapping WHERE caller IN (select id from fhry)
    ) t ON w.entry_id=t.ENTRY_ID AND w.row_num=t.row_num 
    LEFT JOIN fhry ON t.caller = fhry.id WHERE w.ROW_NUM=1
) a
group by ywqqid,czr;

create temporary view wthyybblsc as
select a.name,nvl(b.pjsc,0) pjsc,'JZYY_FHRYWTHPJMBYYBBLSC' as idx_code from fhry a left join(
    select round(avg(sc)/60,2) as pjsc,czr from wthyybblsc_temp d group by czr) b
on a.id=b.czr;


--有退回营业部办理时长
create temporary view ythyybblsc_temp as 
select ywqqid,czr,sum(sjc) as sc
from (
    SELECT w.*,coalesce(fhry.id ,w.caller) AS czr FROM (
		SELECT 
            a2.ywqqid,a3.entry_id,
            UNIX_TIMESTAMP(a3.finish_date, 'yyyyMMdd HH:mm:ss')-UNIX_TIMESTAMP(a3.start_date, 'yyyyMMdd HH:mm:ss') as sjc,
            a3.start_date,
            a3.finish_date,
            a3.caller
            ,ROW_NUMBER() over(PARTITION BY ENTRY_ID ORDER BY caller) AS row_num
		FROM  (
			SELECT a.ywqqid,a.instid,b.state 
			FROM LCGTYW_mapping a 
			LEFT JOIN OS_WFENTRY_mapping b ON a.INSTID =b.ID  
			WHERE a.INSTID <> -1
			UNION 
			SELECT a.ywqqid,a.INSTID,b.state 
			FROM LCGTYW_KHXXXG_mapping a
			LEFT JOIN OS_WFENTRY_mapping b ON a.INSTID =b.id 
			WHERE a.INSTID <> -1
		) a2 
		LEFT JOIN (
			SELECT entry_id,min(start_date) AS start_date,max(finish_date) AS finish_date,caller FROM OS_HISTORYSTEP_mapping WHERE caller IS NOT NULL GROUP BY ENTRY_ID,caller 
		) a3
        ON a2.instid =a3.entry_id
        WHERE  a2.ywqqid IN (select ywqqid from ythid)
            AND a3.caller IN (SELECT czr FROM yybry)
            AND a3.start_date <> a3.finish_date	 
    ) w 
    LEFT JOIN 
    (SELECT ENTRY_ID,caller,ROW_NUMBER() over(PARTITION BY ENTRY_ID ORDER BY caller) AS row_num 
        FROM OS_HISTORYSTEP_mapping WHERE caller IN (select id from fhry)
    ) t ON w.entry_id=t.ENTRY_ID AND w.row_num=t.row_num 
    LEFT JOIN fhry ON t.caller = fhry.id WHERE w.ROW_NUM=1
) a
group by ywqqid,czr;

create temporary view ythyybblsc as
select a.name,nvl(b.pjsc,0) pjsc,'JZYY_FHRYYTHPJMBYYBBLSC' as idx_code from fhry a left join(
    select round(avg(sc)/60,2) as pjsc,czr from ythyybblsc_temp d group by czr) b
on a.id=b.czr;


--总的时长集合
upsert into TIC_YGZB_mapping
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.name as yg, 
  b.pjsc as result
from
  TIC_ZBCS_mapping a,wthblsc b where a.idx_code = 'JZYY_FHRYWTHPJMBBLSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.name as yg, 
  b.pjsc as result
from
  TIC_ZBCS_mapping a,ythblsc b where a.idx_code = 'JZYY_FHRYYTHPJMBBLSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.name as yg, 
  b.pjsc as result
from
  TIC_ZBCS_mapping a,wthdhsc b where a.idx_code = 'JZYY_FHRYWTHPJMBDHSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.name as yg, 
  b.pjsc as result
from
  TIC_ZBCS_mapping a,ythdhsc b where a.idx_code = 'JZYY_FHRYYTHPJMBDHSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.name as yg, 
  b.pjsc as result
from
  TIC_ZBCS_mapping a,wthshsc b where a.idx_code = 'JZYY_FHRYWTHPJMBSHSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.name as yg, 
  b.pjsc as result
from
  TIC_ZBCS_mapping a,ythshsc b where a.idx_code = 'JZYY_FHRYYTHPJMBSHSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.name as yg, 
  b.pjsc as result
from
  TIC_ZBCS_mapping a,wthyybblsc b where a.idx_code = 'JZYY_FHRYWTHPJMBYYBBLSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.name as yg, 
  b.pjsc as result
from
  TIC_ZBCS_mapping a,ythyybblsc b where a.idx_code = 'JZYY_FHRYYTHPJMBYYBBLSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.name as yg, 
  b.pjsc as result
from
  TIC_ZBCS_mapping a,grfhsc b where a.idx_code = 'JZYY_FHGYGRYWSC' 
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.name as yg, 
  b.pjsc as result
from
  TIC_ZBCS_mapping a,jgfhsc b where a.idx_code = 'JZYY_FHGYJGYWSC';


upsert into TIC_YYZB_mapping
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.pjsc as result
from
  TIC_ZBCS_mapping a,rjgrfhsc b where a.idx_code = 'JZYY_RJGRYWFHSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.pjsc as result
from
  TIC_ZBCS_mapping a,rjjgfhsc b where a.idx_code = 'JZYY_RJJGYWFHSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  case when b.pjsc is null and c.pjsc is not null then c.pjsc
       when c.pjsc is null and b.pjsc is not null then b.pjsc 
       else round((nvl(b.pjsc,0)+nvl(c.pjsc,0))/2,2) end as result
from
  TIC_ZBCS_mapping a,rjjgfhsc b, rjgrfhsc c where a.idx_code = 'JZYY_RJFHSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.pjsc as result
from
  TIC_ZBCS_mapping a,wthzblsc b where a.idx_code = 'JZYY_WTHPJMBBLSC';


upsert into TIC_YXZB_mapping
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.pjsc as result
from
  TIC_ZBCS_mapping a,zdhsc b where a.idx_code = 'JZYY_PJDHSC'
  ;

