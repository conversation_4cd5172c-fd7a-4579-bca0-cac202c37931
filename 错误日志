[LOG-PATH]: /home/<USER>/data/dolphinscheduler/taskLogs/18507520943136_16/43179/44042.log, [HOST]:  **************
[INFO] 2025-08-01 15:06:21.643 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[83] - shell task params {"resourceList":[{"id":326,"res":"test-sparkSql-605775166324215808.sql","resourceName":"/445301317922066432/calculation/test-sparkSql-605775166324215808.sql"}],"localParams":[],"rawScript":"#!/bin/bash\n/home/<USER>/data-hub/bin/sparksql-submit.sh --master local[*] --conf spark.app.name=\"【每日】网上开户耗时统计\" --sqlvar RQ=${RQ} --sql $(pwd)/445301317922066432/calculation/test-sparkSql-605775166324215808.sql","dependence":{},"conditionResult":{"successNode":[],"failedNode":[]},"waitStartTimeout":{},"switchResult":{}}
[INFO] 2025-08-01 15:06:21.667 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[137] - raw script : #!/bin/bash
/home/<USER>/data-hub/bin/sparksql-submit.sh --master local[*] --conf spark.app.name="【每日】网上开户耗时统计" --sqlvar RQ=20250801 --sql $(pwd)/445301317922066432/calculation/test-sparkSql-605775166324215808.sql
[INFO] 2025-08-01 15:06:21.667 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[138] - task execute path : /home/<USER>/dolphinscheduler/exec/process/13589517760800/18507520943136_16/43179/44042
[INFO] 2025-08-01 15:06:21.668 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[86] - tenantCode user:livedata, task dir:43179_44042
[INFO] 2025-08-01 15:06:21.668 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[91] - create command file:/home/<USER>/dolphinscheduler/exec/process/13589517760800/18507520943136_16/43179/44042/43179_44042.command
[INFO] 2025-08-01 15:06:21.669 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[117] - command : #!/bin/sh
BASEDIR=$(cd `dirname $0`; pwd)
cd $BASEDIR
source /home/<USER>/dolphinscheduler/conf/env/dolphinscheduler_env.sh
/home/<USER>/dolphinscheduler/exec/process/13589517760800/18507520943136_16/43179/44042/43179_44042_node.sh
[INFO] 2025-08-01 15:06:21.674 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[278] - task run command: sh /home/<USER>/dolphinscheduler/exec/process/13589517760800/18507520943136_16/43179/44042/43179_44042.command
[INFO] 2025-08-01 15:06:21.675 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[170] - process start, process id is: 20822
[INFO] 2025-08-01 15:06:22.676 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[69] -  -> welcome to use bigdata scheduling system...
	2025年 08月 01日 星期五 15:06:21 CST [INFO] Current SPARK Program Installation Location /home/<USER>/spark
	2025年 08月 01日 星期五 15:06:21 CST [INFO] spark conf: --conf spark.sql.sources.partitionOverwriteMode=dynamic --conf spark.sql.parquet.binaryAsString=true --conf spark.master=local[*] --conf spark.app.datahub.empty.exit=false --conf spark.shuffle.useOldFetchProtocol=true --conf spark.executor.memory=2g --conf spark.app.name=livedata --conf spark.executor.heartbeatInterval=200s --conf spark.executor.cores=4 --conf spark.executor.instances=3 --conf spark.network.timeout=500s --conf spark.sql.parquet.writeLegacyFormat=true --conf spark.debug.maxToStringFields=2000 --conf spark.sql.hive.convertMetastoreParquet=true --conf spark.io.compression.codec=snappy --conf spark.sql.parquet.int96RebaseModeInWrite=LEGACY --conf spark.serializer=org.apache.spark.serializer.KryoSerializer
	2025年 08月 01日 星期五 15:06:22 CST [INFO] The file to upload is empty
	2025年 08月 01日 星期五 15:06:22 CST [INFO] The jar file to upload is empty
[INFO] 2025-08-01 15:06:35.681 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[69] -  -> [--config, /home/<USER>/data-hub/config/config-spark-sql.json, --master, local[*], --conf, spark.app.name=【每日】网上开户耗时统计, --sqlvar, RQ=20250801, --sql, /home/<USER>/dolphinscheduler/exec/process/13589517760800/18507520943136_16/43179/44042/445301317922066432/calculation/test-sparkSql-605775166324215808.sql]
	                                                                                              ****#                                             
	                                                                                             *##### ##                                          
	                                                                                             *#**   ##                                          
	                                                                                             *#*    ##                                          
	                                                                                             ##     ##                                          
	        ********      ## *******      ********    *#**     **#*   ********     ********    ###############                                      
	        ########*     ##*######**    **#######*    *#*    **#*   **#######    **######**   ###############                                      
	        **** ***#*    ###******#**  **#*** *****   **#*   *#**   *#*** ***   **#******#**    ##     ##                                          
	               *#*    ##**    **#*  *#*      *#*    **#* *#**    *#*         *#**    **#*    ##     ##                                          
	               *#*    ##*      *#*  *#*      *#*     *#***#*     *#**        *#*      *#*    ##     ##                                          
	        *****####*    ##*      *#*  *#******###*      *###*      *##*****    *#*      *#*    ##     ##                                          
	       **#########    ##        #*  *###########      *###*       **####**   ##        #*    ##     ##                                          
	       *#****   ##    ##*      *#*  *#*              **#*#*         ****##*  *#*      *#*    ##     ##                                          
	       *#*     *##    ##*      *#*  *#*              *#***#*            *#*  *#*      *#*    ##     ##                                          
	       *#*     *##    ##**    **#*  *#**            *#** **#*           *#*  *#**    **#*    ##     *#*                                         
	       *#** ***###    ###******#**  **#*** ****    **#*   *#**   **** ***#*  **#******#**    ##     *#**                                        
	       **######*##    ##*######**    **########   **#*     *#**  ########**   **######**     ##     *#####                                      
	        ******* ##    ## *******      ****#****   *#**     **#*  ****#****     ********      ##      ***##                                      
	                      ##                                                                                                                        
	                      ##                                                                                                                        
	                      ##                                                                                                                        
	                      ##                                                                                                                        
	                      **                                                                                                                        
	--config /home/<USER>/data-hub/config/config-spark-sql.json
	--conf spark.app.name=【每日】网上开户耗时统计
	--sqlvar RQ=20250801
	--sql /home/<USER>/dolphinscheduler/exec/process/13589517760800/18507520943136_16/43179/44042/445301317922066432/calculation/test-sparkSql-605775166324215808.sql
	log4j:WARN No appenders could be found for logger (com.apex.spark.job.RunJob).
	log4j:WARN Please initialize the log4j system properly.
	log4j:WARN See http://logging.apache.org/log4j/1.2/faq.html#noconfig for more info.
	Only support yarn client mode 
	{
	    "spark.app.datahub.empty.exit" : "false",
	    "spark.app.name" : "livedata",
	    "spark.debug.maxToStringFields" : "2000",
	    "spark.driver.extraJavaOptions" : "-XX:+UseG1GC",
	    "spark.driver.memory" : "6g",
	    "spark.executor.cores" : "4",
	    "spark.executor.heartbeatInterval" : "200s",
	    "spark.executor.instances" : "3",
	    "spark.executor.memory" : "2g",
	    "spark.io.compression.codec" : "snappy",
	    "spark.master" : "local[*]",
	    "spark.network.timeout" : "500s",
	    "spark.serializer" : "org.apache.spark.serializer.KryoSerializer",
	    "spark.shuffle.useOldFetchProtocol" : "true",
	    "spark.sql.hive.convertMetastoreParquet" : "true",
	    "spark.sql.parquet.binaryAsString" : "true",
	    "spark.sql.parquet.int96RebaseModeInWrite" : "LEGACY",
	    "spark.sql.parquet.writeLegacyFormat" : "true",
	    "spark.sql.sources.partitionOverwriteMode" : "dynamic"
	}
	
	Debug mode false
	Using Spark's default log4j profile: org/apache/spark/log4j-defaults.properties
	25/08/01 15:06:35 INFO SparkContext: Running Spark version 3.2.4
	25/08/01 15:06:35 INFO ResourceUtils: ==============================================================
	25/08/01 15:06:35 INFO ResourceUtils: No custom resources configured for spark.driver.
	25/08/01 15:06:35 INFO ResourceUtils: ==============================================================
	25/08/01 15:06:35 INFO SparkContext: Submitted application: 【每日】网上开户耗时统计
	25/08/01 15:06:35 INFO ResourceProfile: Default ResourceProfile created, executor resources: Map(cores -> name: cores, amount: 4, script: , vendor: , memory -> name: memory, amount: 2048, script: , vendor: , offHeap -> name: offHeap, amount: 0, script: , vendor: ), task resources: Map(cpus -> name: cpus, amount: 1.0)
	25/08/01 15:06:35 INFO ResourceProfile: Limiting resource is cpus at 4 tasks per executor
	25/08/01 15:06:35 INFO ResourceProfileManager: Added ResourceProfile id: 0
[INFO] 2025-08-01 15:06:36.683 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[69] -  -> 25/08/01 15:06:35 INFO SecurityManager: Changing view acls to: livedata,hive
	25/08/01 15:06:35 INFO SecurityManager: Changing modify acls to: livedata,hive
	25/08/01 15:06:35 INFO SecurityManager: Changing view acls groups to: 
	25/08/01 15:06:35 INFO SecurityManager: Changing modify acls groups to: 
	25/08/01 15:06:35 INFO SecurityManager: SecurityManager: authentication disabled; ui acls disabled; users  with view permissions: Set(livedata, hive); groups with view permissions: Set(); users  with modify permissions: Set(livedata, hive); groups with modify permissions: Set()
	25/08/01 15:06:36 INFO Utils: Successfully started service 'sparkDriver' on port 41606.
	25/08/01 15:06:36 INFO SparkEnv: Registering MapOutputTracker
	25/08/01 15:06:36 INFO SparkEnv: Registering BlockManagerMaster
	25/08/01 15:06:36 INFO BlockManagerMasterEndpoint: Using org.apache.spark.storage.DefaultTopologyMapper for getting topology information
	25/08/01 15:06:36 INFO BlockManagerMasterEndpoint: BlockManagerMasterEndpoint up
	25/08/01 15:06:36 INFO SparkEnv: Registering BlockManagerMasterHeartbeat
	25/08/01 15:06:36 INFO DiskBlockManager: Created local directory at /tmp/blockmgr-e82d2f07-be17-4007-bae1-85c23a4419ec
	25/08/01 15:06:36 INFO MemoryStore: MemoryStore started with capacity 3.4 GiB
[INFO] 2025-08-01 15:06:37.684 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[69] -  -> 25/08/01 15:06:36 INFO SparkEnv: Registering OutputCommitCoordinator
	25/08/01 15:06:37 INFO Utils: Successfully started service 'SparkUI' on port 4040.
	25/08/01 15:06:37 INFO SparkUI: Bound SparkUI to 0.0.0.0, and started at http://trino.master:4040
	25/08/01 15:06:37 INFO SparkContext: Added JAR file:///home/<USER>/data-hub-dist-3.0.0/extlib/commons-collections4-4.4.jar at spark://trino.master:41606/jars/commons-collections4-4.4.jar with timestamp 1754031995547
	25/08/01 15:06:37 INFO SparkContext: Added JAR file:///home/<USER>/data-hub-dist-3.0.0/extlib/elasticsearch-spark-30_2.12-7.16.3.jar at spark://trino.master:41606/jars/elasticsearch-spark-30_2.12-7.16.3.jar with timestamp 1754031995547
	25/08/01 15:06:37 INFO SparkContext: Added JAR file:///home/<USER>/data-hub-dist-3.0.0/extlib/hbase-shaded-client-2.2.1.jar at spark://trino.master:41606/jars/hbase-shaded-client-2.2.1.jar with timestamp 1754031995547
	25/08/01 15:06:37 INFO SparkContext: Added JAR file:///home/<USER>/data-hub-dist-3.0.0/extlib/hbase-shaded-mapreduce-2.0.6.jar at spark://trino.master:41606/jars/hbase-shaded-mapreduce-2.0.6.jar with timestamp 1754031995547
	25/08/01 15:06:37 INFO SparkContext: Added JAR file:///home/<USER>/data-hub-dist-3.0.0/extlib/HiveJDBC41.jar at spark://trino.master:41606/jars/HiveJDBC41.jar with timestamp 1754031995547
	25/08/01 15:06:37 INFO SparkContext: Added JAR file:///home/<USER>/data-hub-dist-3.0.0/extlib/hive-jdbc-shade-driver-1.1.0-master.jar at spark://trino.master:41606/jars/hive-jdbc-shade-driver-1.1.0-master.jar with timestamp 1754031995547
	25/08/01 15:06:37 INFO SparkContext: Added JAR file:///home/<USER>/data-hub-dist-3.0.0/extlib/htrace-core4-4.2.0-incubating.jar at spark://trino.master:41606/jars/htrace-core4-4.2.0-incubating.jar with timestamp 1754031995547
	25/08/01 15:06:37 INFO SparkContext: Added JAR file:/home/<USER>/data-hub-dist-3.0.0/lib/spark-clients-*******-shaded.jar at spark://trino.master:41606/jars/spark-clients-*******-shaded.jar with timestamp 1754031995547
	25/08/01 15:06:37 INFO Executor: Starting executor ID driver on host trino.master
	25/08/01 15:06:37 INFO Executor: Fetching spark://trino.master:41606/jars/hbase-shaded-client-2.2.1.jar with timestamp 1754031995547
	25/08/01 15:06:37 INFO TransportClientFactory: Successfully created connection to trino.master/**************:41606 after 42 ms (0 ms spent in bootstraps)
	25/08/01 15:06:37 INFO Utils: Fetching spark://trino.master:41606/jars/hbase-shaded-client-2.2.1.jar to /tmp/spark-eb005031-8f7b-4508-9178-dc17f2206d67/userFiles-f66a113f-07eb-48ec-a868-f8d38f024f05/fetchFileTemp7414423807132860180.tmp
[INFO] 2025-08-01 15:06:38.685 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[69] -  -> 25/08/01 15:06:37 INFO Executor: Adding file:/tmp/spark-eb005031-8f7b-4508-9178-dc17f2206d67/userFiles-f66a113f-07eb-48ec-a868-f8d38f024f05/hbase-shaded-client-2.2.1.jar to class loader
	25/08/01 15:06:37 INFO Executor: Fetching spark://trino.master:41606/jars/spark-clients-*******-shaded.jar with timestamp 1754031995547
	25/08/01 15:06:37 INFO Utils: Fetching spark://trino.master:41606/jars/spark-clients-*******-shaded.jar to /tmp/spark-eb005031-8f7b-4508-9178-dc17f2206d67/userFiles-f66a113f-07eb-48ec-a868-f8d38f024f05/fetchFileTemp5525116292954066886.tmp
	25/08/01 15:06:38 INFO Executor: Adding file:/tmp/spark-eb005031-8f7b-4508-9178-dc17f2206d67/userFiles-f66a113f-07eb-48ec-a868-f8d38f024f05/spark-clients-*******-shaded.jar to class loader
	25/08/01 15:06:38 INFO Executor: Fetching spark://trino.master:41606/jars/htrace-core4-4.2.0-incubating.jar with timestamp 1754031995547
	25/08/01 15:06:38 INFO Utils: Fetching spark://trino.master:41606/jars/htrace-core4-4.2.0-incubating.jar to /tmp/spark-eb005031-8f7b-4508-9178-dc17f2206d67/userFiles-f66a113f-07eb-48ec-a868-f8d38f024f05/fetchFileTemp7782200396005691384.tmp
	25/08/01 15:06:38 INFO Executor: Adding file:/tmp/spark-eb005031-8f7b-4508-9178-dc17f2206d67/userFiles-f66a113f-07eb-48ec-a868-f8d38f024f05/htrace-core4-4.2.0-incubating.jar to class loader
	25/08/01 15:06:38 INFO Executor: Fetching spark://trino.master:41606/jars/hbase-shaded-mapreduce-2.0.6.jar with timestamp 1754031995547
	25/08/01 15:06:38 INFO Utils: Fetching spark://trino.master:41606/jars/hbase-shaded-mapreduce-2.0.6.jar to /tmp/spark-eb005031-8f7b-4508-9178-dc17f2206d67/userFiles-f66a113f-07eb-48ec-a868-f8d38f024f05/fetchFileTemp1414142064122401263.tmp
	25/08/01 15:06:38 INFO Executor: Adding file:/tmp/spark-eb005031-8f7b-4508-9178-dc17f2206d67/userFiles-f66a113f-07eb-48ec-a868-f8d38f024f05/hbase-shaded-mapreduce-2.0.6.jar to class loader
	25/08/01 15:06:38 INFO Executor: Fetching spark://trino.master:41606/jars/elasticsearch-spark-30_2.12-7.16.3.jar with timestamp 1754031995547
	25/08/01 15:06:38 INFO Utils: Fetching spark://trino.master:41606/jars/elasticsearch-spark-30_2.12-7.16.3.jar to /tmp/spark-eb005031-8f7b-4508-9178-dc17f2206d67/userFiles-f66a113f-07eb-48ec-a868-f8d38f024f05/fetchFileTemp3637009706231178431.tmp
	25/08/01 15:06:38 INFO Executor: Adding file:/tmp/spark-eb005031-8f7b-4508-9178-dc17f2206d67/userFiles-f66a113f-07eb-48ec-a868-f8d38f024f05/elasticsearch-spark-30_2.12-7.16.3.jar to class loader
	25/08/01 15:06:38 INFO Executor: Fetching spark://trino.master:41606/jars/commons-collections4-4.4.jar with timestamp 1754031995547
	25/08/01 15:06:38 INFO Utils: Fetching spark://trino.master:41606/jars/commons-collections4-4.4.jar to /tmp/spark-eb005031-8f7b-4508-9178-dc17f2206d67/userFiles-f66a113f-07eb-48ec-a868-f8d38f024f05/fetchFileTemp3343111367234990036.tmp
	25/08/01 15:06:38 INFO Executor: Adding file:/tmp/spark-eb005031-8f7b-4508-9178-dc17f2206d67/userFiles-f66a113f-07eb-48ec-a868-f8d38f024f05/commons-collections4-4.4.jar to class loader
	25/08/01 15:06:38 INFO Executor: Fetching spark://trino.master:41606/jars/hive-jdbc-shade-driver-1.1.0-master.jar with timestamp 1754031995547
	25/08/01 15:06:38 INFO Utils: Fetching spark://trino.master:41606/jars/hive-jdbc-shade-driver-1.1.0-master.jar to /tmp/spark-eb005031-8f7b-4508-9178-dc17f2206d67/userFiles-f66a113f-07eb-48ec-a868-f8d38f024f05/fetchFileTemp179397598058989625.tmp
	25/08/01 15:06:38 INFO Executor: Adding file:/tmp/spark-eb005031-8f7b-4508-9178-dc17f2206d67/userFiles-f66a113f-07eb-48ec-a868-f8d38f024f05/hive-jdbc-shade-driver-1.1.0-master.jar to class loader
	25/08/01 15:06:38 INFO Executor: Fetching spark://trino.master:41606/jars/HiveJDBC41.jar with timestamp 1754031995547
	25/08/01 15:06:38 INFO Utils: Fetching spark://trino.master:41606/jars/HiveJDBC41.jar to /tmp/spark-eb005031-8f7b-4508-9178-dc17f2206d67/userFiles-f66a113f-07eb-48ec-a868-f8d38f024f05/fetchFileTemp5449825762001777687.tmp
	25/08/01 15:06:38 INFO Executor: Adding file:/tmp/spark-eb005031-8f7b-4508-9178-dc17f2206d67/userFiles-f66a113f-07eb-48ec-a868-f8d38f024f05/HiveJDBC41.jar to class loader
	25/08/01 15:06:38 INFO Utils: Successfully started service 'org.apache.spark.network.netty.NettyBlockTransferService' on port 43087.
	25/08/01 15:06:38 INFO NettyBlockTransferService: Server created on trino.master:43087
	25/08/01 15:06:38 INFO BlockManager: Using org.apache.spark.storage.RandomBlockReplicationPolicy for block replication policy
	25/08/01 15:06:38 INFO BlockManagerMaster: Registering BlockManager BlockManagerId(driver, trino.master, 43087, None)
	25/08/01 15:06:38 INFO BlockManagerMasterEndpoint: Registering block manager trino.master:43087 with 3.4 GiB RAM, BlockManagerId(driver, trino.master, 43087, None)
	25/08/01 15:06:38 INFO BlockManagerMaster: Registered BlockManager BlockManagerId(driver, trino.master, 43087, None)
	25/08/01 15:06:38 INFO BlockManager: Initialized BlockManager: BlockManagerId(driver, trino.master, 43087, None)
[INFO] 2025-08-01 15:06:39.687 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[69] -  -> 25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver org.apache.hive.jdbc.HiveDriver
	25/08/01 15:06:39 WARN JdbcUtil: Uninstall the driver org.apache.hive.jdbc.HiveDriver
	25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver org.apache.derby.jdbc.AutoloadedDriver
	25/08/01 15:06:39 WARN JdbcUtil: Registered driver name org.apache.derby.jdbc.AutoloadedDriver
	25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver org.apache.calcite.jdbc.Driver
	25/08/01 15:06:39 WARN JdbcUtil: Registered driver name org.apache.calcite.jdbc.Driver
	25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver org.apache.calcite.avatica.remote.Driver
	25/08/01 15:06:39 WARN JdbcUtil: Registered driver name org.apache.calcite.avatica.remote.Driver
	25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver org.h2.Driver
	25/08/01 15:06:39 WARN JdbcUtil: Registered driver name org.h2.Driver
	25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver com.mysql.cj.jdbc.Driver
	25/08/01 15:06:39 WARN JdbcUtil: Registered driver name com.mysql.cj.jdbc.Driver
	25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver oracle.jdbc.OracleDriver
	25/08/01 15:06:39 WARN JdbcUtil: Registered driver name oracle.jdbc.OracleDriver
	25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver org.postgresql.Driver
	25/08/01 15:06:39 WARN JdbcUtil: Registered driver name org.postgresql.Driver
	25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver org.apache.ignite.IgniteJdbcDriver
	25/08/01 15:06:39 WARN JdbcUtil: Registered driver name org.apache.ignite.IgniteJdbcDriver
	25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver org.apache.ignite.IgniteJdbcThinDriver
	25/08/01 15:06:39 WARN JdbcUtil: Registered driver name org.apache.ignite.IgniteJdbcThinDriver
	25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver com.gbase.jdbc.Driver
	25/08/01 15:06:39 WARN JdbcUtil: Registered driver name com.gbase.jdbc.Driver
	25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver dm.jdbc.driver.DmDriver
	25/08/01 15:06:39 WARN JdbcUtil: Registered driver name dm.jdbc.driver.DmDriver
	25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver com.kingbase8.Driver
	25/08/01 15:06:39 WARN JdbcUtil: Registered driver name com.kingbase8.Driver
	25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver com.pivotal.jdbc.GreenplumDriver
	25/08/01 15:06:39 WARN JdbcUtil: Registered driver name com.pivotal.jdbc.GreenplumDriver
	25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver com.oceanbase.jdbc.Driver
	25/08/01 15:06:39 WARN JdbcUtil: Registered driver name com.oceanbase.jdbc.Driver
	25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver com.ibm.db2.jcc.DB2Driver
	25/08/01 15:06:39 WARN JdbcUtil: Registered driver name com.ibm.db2.jcc.DB2Driver
	25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver com.microsoft.sqlserver.jdbc.SQLServerDriver
	25/08/01 15:06:39 WARN JdbcUtil: Registered driver name com.microsoft.sqlserver.jdbc.SQLServerDriver
	25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver com.cloudera.hive.jdbc41.HS2Driver
	25/08/01 15:06:39 WARN JdbcUtil: Registered driver name com.cloudera.hive.jdbc41.HS2Driver
	25/08/01 15:06:39 WARN JdbcUtil: Found a registered JDBC driver com.cloudera.hive.jdbc41.HS1Driver
	25/08/01 15:06:39 WARN JdbcUtil: Registered driver name com.cloudera.hive.jdbc41.HS1Driver
	25/08/01 15:06:39 INFO RunJob: Find the user SQL workspace schema path /home/<USER>/data-hub/config/schema 
	25/08/01 15:06:39 INFO RunJob: Find the user SQL workspace udf path ./config/udf 
	25/08/01 15:06:39 INFO RunJob: Config file /home/<USER>/data-hub/config/config-spark-sql.json
	25/08/01 15:06:39 INFO RunJob: Read stream parameters  from config file 
	25/08/01 15:06:39 INFO RunJob: Set stream mode Update
	25/08/01 15:06:39 INFO RunJob: Find the user SQL workspace udf jar path /home/<USER>/data-hub/config/udf 
	25/08/01 15:06:39 INFO RunJob: DDL file path /ddl
	Setting Dynamic SQL Workspace Usage Parameter spark.app.datahub.dynamic.workspace Assignments
	Enable yarn client mode or local mode. The working space is 
	Read SQL file  /home/<USER>/dolphinscheduler/exec/process/13589517760800/18507520943136_16/43179/44042/445301317922066432/calculation/test-sparkSql-605775166324215808.sql
	Number of SQL file lines read 853
	25/08/01 15:06:39 INFO RunJob: Parse sql var parameters
	25/08/01 15:06:39 INFO RunJob: Metric log4j 
	Start registering SQL connectors
	25/08/01 15:06:39 INFO RunJob: Launch engine spark
[INFO] 2025-08-01 15:06:40.688 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[69] -  -> 25/08/01 15:06:40 INFO SparkSession: Calcite successfully resolved the input SQL statement and the attribute 6 resolution schema 326 resolution tableName LCFXCKH_mapping
[INFO] 2025-08-01 15:06:41.689 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[69] -  -> 25/08/01 15:06:40 INFO SecurityUtil: decrypt successful !
	25/08/01 15:06:40 INFO JdbcBatchSource: Set the number of concurrent data queries to 5
	25/08/01 15:06:40 WARN JdbcBatchSource: When you use strings you can use mod function or hash function to slice pushdown=false
	25/08/01 15:06:40 INFO JdbcBatchSource: Analyze user input primary key information to randomly generate slice function ID
	25/08/01 15:06:40 INFO JdbcBatchSource: host://************* port:1521 param:
	25/08/01 15:06:40 INFO JdbcBatchSource: The analysis slice function is mod(ora_hash(ID),5)
	25/08/01 15:06:40 INFO JdbcBatchSource: Query: SELECT ID, INSTID, SQRQ, SQSJ, KHFS, KHZD, JGBZ, YWQQID, YWDM, FQQD, YYB, GTKHH, KHLY, KHXM, KHQC, DJR, SHGY, CZZD, KHXZR, JZR, QDBM, YXRY, DN, ZJLB, ZJBH, XB, CSRQ, ZJYXQ, ZJDZ, ZJDZYB, ZJFZJG, ZJZP, ZJYZLY, EDZ, YYZZNJRQ, DZ, YZBM, JTDZ, JTDZYB, SJ, DH, CZ, EMAIL, ZYDM, HYZK, XL, GZDW, GZDWDZ, MZDM, JG, GJ, PROVINCE, CITY, SEC, TBSM, FXQHYLB, XQFXDJ, QYXZ, HYDM, FRLB, YWMC, JGGSDH, JGZCDZ, JGZCRQ, JGJYFW, JGZCZB, JGZGB, JGLTGB, ZZJGDM, ZZJGDMYXQ, ZZJGDMFZJG, ZZJGDMNJRQ, SWDJZ, SWDJZYXQ, SWDJZFZJG, SWDJZNJRQ, FRDBXM, FRDBZJLB, FRDBZJBH, FRDBZJYXQ, JBRXM, JBRZJLB, JBRZJBH, JBRZJYXQ, JBRXB, JBRDH, JBRSJ, BZ, KHQZ, KHKH, WTFS, FWXM, FXJB, FXCSNL, GPFXCSNL, SFTB, JYMM, ZJMM, FWMM, SQJJZH, GDKH_SH, GDDJ_SH, GDKH_HJ, GDDJ_HJ, GDKH_HB, GDDJ_HB, GDKH_SZ, GDDJ_SZ, GDKH_SJ, GDDJ_SJ, GDKH_SB, GDDJ_SB, GDKH_TA, GDDJ_TA, GDKH_TU, GDDJ_TU, GDJYQX_SH, GDJYQX_HJ, GDJYQX_HB, GDJYQX_SZ, GDJYQX_SJ, GDJYQX_SB, GDJYQX_TA, GDJYQX_TU, SHZDJY, HBZDJY, SFWLFW, WLFWMM, CYBKT, CYBKTFS, CGYHZH, CGYHMM, YHDM_USD, DJFS_USD, YHZH_USD, YHMM_USD, YHDM_HKD, DJFS_HKD, YHZH_HKD, YHMM_HKD, KHZP, KHSP, HFRQ, HFSJ, HFGYDM, HFGYXM, HFJG, HFJGSM, HFTJ, SHZT, STEP, CLJGSM, FHYJ, WJID, TMDAC, HFWJID, HFTMDAC, IBY1, IBY2, IBY3, CBY1, CBY2, CBY3, JGLB, ZBSX, GYSX, JGWZDZ, ZZJGDMZJDZ, SPLX, RLDBSBL, RLGY, YSBZ, YSXX, CPDF, YJZT, SSSX, JGZCBZ, SMJJGLRBM, JZRQ, KGGD, KGGDZJLB, KGGDZJBH, KGGDZJQSRQ, KGGDZJJZRQ, SJKZR, SJSYR, ZW, BLCXJL, ZZSM, ZJQSRQ, SSJMSF, SSJGLB, KZRXM, KZRSSJMSF, KZRCSRQ, NAME_X, NAME_M, JGMCE, JGDZ_GJ, JGDZ_SF, JGDZ_CS, JGDZ, JGDZE_GJ, JGDZE_SF, JGDZE_CS, JGDZE, XJDZ_GJ, XJDZ_SF, XJDZ_CS, XJDZ, XJDZE_GJ, XJDZE_SF, XJDZE_CS, XJDZE, CSD_GJ, CSD_SF, CSD_CS, CSD, CSDE_GJ, CSDE_SF, CSDE_CS, CSDE, SSJMG1, NSRSBH1, SSJMG2, NSRSBH2, SSJMG3, NSRSBH3, WNSRSBHYY, JGSSJMG1, JGNSRSBH1, JGSSJMG2, JGNSRSBH2, JGSSJMG3, JGNSRSBH3, JGWNSRSBHYY, RHZXSQ, TDKHMD, JGWNSRSBHJTYY, WNSRSBHJTYY, ZZZSBH, SFWGSGLF, ZJLY, FZRXM, FZRZJLB, FZRZJBH, FZRZJQSRQ, FZRZJJZRQ, CPCSGGBL, TDZRR, CPBZ, CYRLB, CPCLSJ, CPDQRQ, CPJG, CPGM, SYTZ, CPLB, GLRMC, GLRZJLB, GLRZJBH, GLRZJQSRQ, GLRZJJZRQ, GLRDH, TGRMC, TGRZJLB, TGRZJBH, TGRZJQSRQ, TGRZJJZRQ, TGRDH, CPBASJ, CPBABH, CPBAJG, CPTGMS, SLVIDEO, ZZSMHQZT, AREANO, BRNO, SALENO, SRXX, ZZJGDMQSRQ, FRDBZJQSRQ, JBRZJQSRQ, DSSWDJZ, DSSWDJZFZJG, DSSWDJZNJRQ, DSSWDJJZRQ, SWDJZQSRQ, DSSWDJZQSRQ, FJXX, QTZYXX, QTFXQHYLBXX, NSR, ZDKHH, CPDM, HFLYURL, SSGPDM, SSDD, FZZJLB, FZZJBH, FZZJJZRQ, FZZJDZ, DJRQ, DJSJ, SHRQ, SHSJ, JQRSHZT, JQRSHBZ, JQRSHRQ, JQRSHSJ, JQRSHJD, SCCWYY, HFFS, HFZXJG, HFZXBZ, HFSBCS, HFRWZT, ZJDZTB, WBYWLSH, WBQDBZ, A5, A5_KHLX, TZZFL, KHXJ, KHDCWJID, ZWXM FROM CIF.LCFXCKH
	25/08/01 15:06:40 INFO JdbcBatchSource: User input batch pull data volume is empty, use default value fetchsize=1000
	25/08/01 15:06:40 INFO JdbcBatchSource: User configuration submit timeout is empty, use default time queryTimeout=600
	25/08/01 15:06:40 INFO JdbcBatchSource: Get database name CIF
	25/08/01 15:06:40 INFO JdbcBatchSource: Get table name LCFXCKH
	25/08/01 15:06:40 INFO SecurityUtil: decrypt failed !
	25/08/01 15:06:40 INFO SecurityUtil: decrypt failed ! 
	25/08/01 15:06:41 INFO PluginUtil: Load class com.apex.spark.source.JdbcBatchSource 
	25/08/01 15:06:41 INFO SparkSession: Calcite successfully resolved the input SQL statement and the attribute 6 resolution schema 17 resolution tableName TYWQQ_CZLS_mapping
	25/08/01 15:06:41 INFO SecurityUtil: decrypt successful !
	25/08/01 15:06:41 INFO JdbcBatchSource: Set the number of concurrent data queries to 5
	25/08/01 15:06:41 WARN JdbcBatchSource: When you use strings you can use mod function or hash function to slice pushdown=false
	25/08/01 15:06:41 INFO JdbcBatchSource: Analyze user input primary key information to randomly generate slice function ID
	25/08/01 15:06:41 INFO JdbcBatchSource: host://************* port:1521 param:
	25/08/01 15:06:41 INFO JdbcBatchSource: The analysis slice function is mod(ora_hash(ID),5)
	25/08/01 15:06:41 INFO JdbcBatchSource: Query: SELECT ID, KHH, YWQQID, CZR, CZRQ, CZSJ, CZZD, CZLX, YWQQZT, BZSM, ACTION, JDMC, FLAG, HISTORYSTEPID, CZYS, ORGID, JDDM FROM CIF.TYWQQ_CZLS
	25/08/01 15:06:41 INFO JdbcBatchSource: User input batch pull data volume is empty, use default value fetchsize=1000
	25/08/01 15:06:41 INFO JdbcBatchSource: User configuration submit timeout is empty, use default time queryTimeout=600
	25/08/01 15:06:41 INFO JdbcBatchSource: Get database name CIF
	25/08/01 15:06:41 INFO JdbcBatchSource: Get table name TYWQQ_CZLS
	25/08/01 15:06:41 INFO SecurityUtil: decrypt failed !
	25/08/01 15:06:41 INFO SecurityUtil: decrypt failed ! 
	25/08/01 15:06:41 INFO PluginUtil: Load class com.apex.spark.source.JdbcBatchSource 
	25/08/01 15:06:41 INFO SparkSession: Calcite successfully resolved the input SQL statement and the attribute 6 resolution schema 18 resolution tableName TIC_ZBCS_mapping
	25/08/01 15:06:41 INFO SecurityUtil: decrypt successful !
	25/08/01 15:06:41 INFO JdbcBatchSource: Set the number of concurrent data queries to 5
	25/08/01 15:06:41 WARN JdbcBatchSource: When you use strings you can use mod function or hash function to slice pushdown=false
	25/08/01 15:06:41 INFO JdbcBatchSource: Analyze user input primary key information to randomly generate slice function ID
	25/08/01 15:06:41 INFO JdbcBatchSource: host:null port:null param:
	25/08/01 15:06:41 INFO JdbcBatchSource: The analysis slice function is mod(ora_hash(ID),5)
	25/08/01 15:06:41 INFO JdbcBatchSource: Query: SELECT ID, FID, GRADE, TYPE, NAME, FDNCODE, IDX_CODE, IDX_CODE_SRC, IDX_NM, IDX_DISPLAY_NAME, IDX_CL, IDX_GRD, STATUS, DESCR, PRCS, DEPARTMENT, ZBLX, INDEX_FLAG FROM DOAMP.TIC_ZBCS
	25/08/01 15:06:41 INFO JdbcBatchSource: User input batch pull data volume is empty, use default value fetchsize=1000
	25/08/01 15:06:41 INFO JdbcBatchSource: User configuration submit timeout is empty, use default time queryTimeout=600
	25/08/01 15:06:41 INFO JdbcBatchSource: Get database name DOAMP
	25/08/01 15:06:41 INFO JdbcBatchSource: Get table name TIC_ZBCS
	25/08/01 15:06:41 INFO SecurityUtil: decrypt failed !
	25/08/01 15:06:41 INFO SecurityUtil: decrypt failed ! 
	25/08/01 15:06:41 INFO PluginUtil: Load class com.apex.spark.source.JdbcBatchSource 
	25/08/01 15:06:41 INFO SparkSession: Initialize the plugin runtime platform Linux
	25/08/01 15:06:41 INFO SparkSession: Initialize the plugin runtime platform Linux
	25/08/01 15:06:41 INFO SparkSession: Initialize the plugin runtime platform Linux
	25/08/01 15:06:41 INFO SparkSession: Initialize the plugin runtime platform Linux
	25/08/01 15:06:41 INFO SparkSession: Initialize the plugin runtime platform Linux
	25/08/01 15:06:41 INFO SparkSession: Initialize the plugin runtime platform Linux
	25/08/01 15:06:41 INFO JdbcBatchSink: Convert field names to uppercase RQ=>RQ
	25/08/01 15:06:41 INFO JdbcBatchSink: Convert field names to uppercase IDX_ID=>IDX_ID
	25/08/01 15:06:41 INFO JdbcBatchSink: Convert field names to uppercase IDX_CODE=>IDX_CODE
	25/08/01 15:06:41 INFO JdbcBatchSink: Convert field names to uppercase RESULT=>RESULT
	25/08/01 15:06:41 INFO JdbcBatchSink: Convert field names to uppercase CYCLE=>CYCLE
	25/08/01 15:06:41 WARN JdbcBatchSink: Invalid sharding mode parameter name does not match rollback to original value sharding.table=DOAMP.TIC_YXZB db.table=DOAMP.TIC_YXZB
	25/08/01 15:06:41 INFO SecurityUtil: decrypt successful !
	25/08/01 15:06:41 INFO JdbcBatchSink: host:null port:null param:
	25/08/01 15:06:41 INFO JdbcBatchSink: jdbc:dm://**************:5236 If you need to override please write properties.isolationLevel=READ_COMMITTED Current modified transaction isolation level isolationLevel=READ_COMMITTED
	25/08/01 15:06:41 INFO JdbcBatchSink: Save the current database name null
	25/08/01 15:06:41 INFO JdbcBatchSink: User input batch submit data volume is empty, use default value batchsize=1000
	25/08/01 15:06:41 INFO JdbcBatchSink: User configuration submit timeout is empty, use default time queryTimeout=600
	25/08/01 15:06:41 WARN JdbcBatchSink: Repartitioning requires the enable parameter spark.app.datahub.partition.convert=true
	25/08/01 15:06:41 INFO JdbcBatchSink: User input custom primary key policy RQ,IDX_ID
	25/08/01 15:06:41 INFO JdbcBatchSink: Get database name DOAMP
	25/08/01 15:06:41 INFO JdbcBatchSink: Get table name TIC_YXZB
	25/08/01 15:06:41 INFO SecurityUtil: decrypt failed !
	25/08/01 15:06:41 INFO SecurityUtil: decrypt failed ! 
[INFO] 2025-08-01 15:06:43.692 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[69] -  -> 25/08/01 15:06:42 INFO PluginUtil: Load class com.apex.spark.sink.JdbcBatchSink 
	25/08/01 15:06:43 INFO SparkSession: Initialize the plugin runtime platform Linux
	25/08/01 15:06:43 INFO SparkSession: Initialize the plugin runtime platform Linux
	25/08/01 15:06:43 INFO SparkSession: Initialize the plugin runtime platform Linux
	25/08/01 15:06:43 INFO SparkSession: Initialize the plugin runtime platform Linux
	25/08/01 15:06:43 INFO SparkSession: Initialize the plugin runtime platform Linux
	25/08/01 15:06:43 INFO SparkSession: Initialize the plugin runtime platform Linux
	25/08/01 15:06:43 INFO JdbcBatchSink: Convert field names to uppercase RQ=>RQ
	25/08/01 15:06:43 INFO JdbcBatchSink: Convert field names to uppercase YG=>YG
	25/08/01 15:06:43 INFO JdbcBatchSink: Convert field names to uppercase IDX_ID=>IDX_ID
	25/08/01 15:06:43 INFO JdbcBatchSink: Convert field names to uppercase IDX_CODE=>IDX_CODE
	25/08/01 15:06:43 INFO JdbcBatchSink: Convert field names to uppercase RESULT=>RESULT
	25/08/01 15:06:43 INFO JdbcBatchSink: Convert field names to uppercase CYCLE=>CYCLE
	25/08/01 15:06:43 WARN JdbcBatchSink: Invalid sharding mode parameter name does not match rollback to original value sharding.table=DOAMP.TIC_YGZB db.table=DOAMP.TIC_YGZB
	25/08/01 15:06:43 INFO SecurityUtil: decrypt successful !
	25/08/01 15:06:43 INFO JdbcBatchSink: host:null port:null param:
	25/08/01 15:06:43 INFO JdbcBatchSink: jdbc:dm://**************:5236 If you need to override please write properties.isolationLevel=READ_COMMITTED Current modified transaction isolation level isolationLevel=READ_COMMITTED
	25/08/01 15:06:43 INFO JdbcBatchSink: Save the current database name null
	25/08/01 15:06:43 INFO JdbcBatchSink: User input batch submit data volume is empty, use default value batchsize=1000
	25/08/01 15:06:43 INFO JdbcBatchSink: User configuration submit timeout is empty, use default time queryTimeout=600
	25/08/01 15:06:43 WARN JdbcBatchSink: Repartitioning requires the enable parameter spark.app.datahub.partition.convert=true
	25/08/01 15:06:43 INFO JdbcBatchSink: User input custom primary key policy RQ,YG,IDX_ID
	25/08/01 15:06:43 INFO JdbcBatchSink: Get database name DOAMP
	25/08/01 15:06:43 INFO JdbcBatchSink: Get table name TIC_YGZB
	25/08/01 15:06:43 INFO SecurityUtil: decrypt failed !
	25/08/01 15:06:43 INFO SecurityUtil: decrypt failed ! 
	25/08/01 15:06:43 INFO PluginUtil: Load class com.apex.spark.sink.JdbcBatchSink 
	25/08/01 15:06:43 INFO SparkSession: Enable Data Lake Execution mode --conf spark.app.datahub.datalake.enabled=true
	25/08/01 15:06:43 INFO SparkSession: Enable hive log debug mode parameter configuration --conf spark.app.datahub.hive.log.debug=true
	25/08/01 15:06:43 INFO PluginUtil: Load class com.apex.spark.transform.Sql 
	Ready to start SQL engine, execute mode batch
	25/08/01 15:06:43 INFO RunJob: Launch application number of source connector 3 , Number of transform connector 1 Number of sink connector 2 
	25/08/01 15:06:43 INFO RunJob: Set spark run mode pluginsSql 
	25/08/01 15:06:43 INFO SharedState: Setting hive.metastore.warehouse.dir ('null') to the value of spark.sql.warehouse.dir.
	25/08/01 15:06:43 INFO SharedState: Warehouse path is 'file:/home/<USER>/dolphinscheduler/exec/process/13589517760800/18507520943136_16/43179/44042/spark-warehouse'.
[INFO] 2025-08-01 15:06:44.693 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[69] -  -> 25/08/01 15:06:44 INFO Version: Elasticsearch Hadoop v7.16.3 [eb87a395c3]
[INFO] 2025-08-01 15:06:48.695 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[69] -  -> 25/08/01 15:06:47 WARN JdbcUtil: execute SELECT MAX(ID) as max_value,MIN(ID) as min_value FROM CIF.LCFXCKH
	25/08/01 15:06:47 WARN JdbcBatchSource: Get automatic inference information true
	25/08/01 15:06:47 WARN JdbcBatchSource: Find if partition column is of type string ID
	25/08/01 15:06:47 INFO JdbcBatchSource: Set Dynamic Partitioning Inference List Usage Parameters spark.app.datahub.jdbc.dynamic.partition.list
	25/08/01 15:06:47 INFO JdbcBatchSource: Override Dynamic Partitioning Inference Default Number of Partitions Usage Parameter spark.app.datahub.jdbc.default.numPartitions
	25/08/01 15:06:47 INFO JdbcBatchSource: Override default partition tile function use parameters spark.app.datahub.jdbc.default.splitFunc
	25/08/01 15:06:47 WARN JdbcBatchSource: Console entry of the above parameters is not global. Default Overrides default values only
	25/08/01 15:06:47 WARN JdbcBatchSource: Partition inference using user-defined types pushdown=true
	25/08/01 15:06:47 INFO JdbcBatchSource: Use user-defined query SQL SELECT ID, INSTID, SQRQ, SQSJ, KHFS, KHZD, JGBZ, YWQQID, YWDM, FQQD, YYB, GTKHH, KHLY, KHXM, KHQC, DJR, SHGY, CZZD, KHXZR, JZR, QDBM, YXRY, DN, ZJLB, ZJBH, XB, CSRQ, ZJYXQ, ZJDZ, ZJDZYB, ZJFZJG, ZJZP, ZJYZLY, EDZ, YYZZNJRQ, DZ, YZBM, JTDZ, JTDZYB, SJ, DH, CZ, EMAIL, ZYDM, HYZK, XL, GZDW, GZDWDZ, MZDM, JG, GJ, PROVINCE, CITY, SEC, TBSM, FXQHYLB, XQFXDJ, QYXZ, HYDM, FRLB, YWMC, JGGSDH, JGZCDZ, JGZCRQ, JGJYFW, JGZCZB, JGZGB, JGLTGB, ZZJGDM, ZZJGDMYXQ, ZZJGDMFZJG, ZZJGDMNJRQ, SWDJZ, SWDJZYXQ, SWDJZFZJG, SWDJZNJRQ, FRDBXM, FRDBZJLB, FRDBZJBH, FRDBZJYXQ, JBRXM, JBRZJLB, JBRZJBH, JBRZJYXQ, JBRXB, JBRDH, JBRSJ, BZ, KHQZ, KHKH, WTFS, FWXM, FXJB, FXCSNL, GPFXCSNL, SFTB, JYMM, ZJMM, FWMM, SQJJZH, GDKH_SH, GDDJ_SH, GDKH_HJ, GDDJ_HJ, GDKH_HB, GDDJ_HB, GDKH_SZ, GDDJ_SZ, GDKH_SJ, GDDJ_SJ, GDKH_SB, GDDJ_SB, GDKH_TA, GDDJ_TA, GDKH_TU, GDDJ_TU, GDJYQX_SH, GDJYQX_HJ, GDJYQX_HB, GDJYQX_SZ, GDJYQX_SJ, GDJYQX_SB, GDJYQX_TA, GDJYQX_TU, SHZDJY, HBZDJY, SFWLFW, WLFWMM, CYBKT, CYBKTFS, CGYHZH, CGYHMM, YHDM_USD, DJFS_USD, YHZH_USD, YHMM_USD, YHDM_HKD, DJFS_HKD, YHZH_HKD, YHMM_HKD, KHZP, KHSP, HFRQ, HFSJ, HFGYDM, HFGYXM, HFJG, HFJGSM, HFTJ, SHZT, STEP, CLJGSM, FHYJ, WJID, TMDAC, HFWJID, HFTMDAC, IBY1, IBY2, IBY3, CBY1, CBY2, CBY3, JGLB, ZBSX, GYSX, JGWZDZ, ZZJGDMZJDZ, SPLX, RLDBSBL, RLGY, YSBZ, YSXX, CPDF, YJZT, SSSX, JGZCBZ, SMJJGLRBM, JZRQ, KGGD, KGGDZJLB, KGGDZJBH, KGGDZJQSRQ, KGGDZJJZRQ, SJKZR, SJSYR, ZW, BLCXJL, ZZSM, ZJQSRQ, SSJMSF, SSJGLB, KZRXM, KZRSSJMSF, KZRCSRQ, NAME_X, NAME_M, JGMCE, JGDZ_GJ, JGDZ_SF, JGDZ_CS, JGDZ, JGDZE_GJ, JGDZE_SF, JGDZE_CS, JGDZE, XJDZ_GJ, XJDZ_SF, XJDZ_CS, XJDZ, XJDZE_GJ, XJDZE_SF, XJDZE_CS, XJDZE, CSD_GJ, CSD_SF, CSD_CS, CSD, CSDE_GJ, CSDE_SF, CSDE_CS, CSDE, SSJMG1, NSRSBH1, SSJMG2, NSRSBH2, SSJMG3, NSRSBH3, WNSRSBHYY, JGSSJMG1, JGNSRSBH1, JGSSJMG2, JGNSRSBH2, JGSSJMG3, JGNSRSBH3, JGWNSRSBHYY, RHZXSQ, TDKHMD, JGWNSRSBHJTYY, WNSRSBHJTYY, ZZZSBH, SFWGSGLF, ZJLY, FZRXM, FZRZJLB, FZRZJBH, FZRZJQSRQ, FZRZJJZRQ, CPCSGGBL, TDZRR, CPBZ, CYRLB, CPCLSJ, CPDQRQ, CPJG, CPGM, SYTZ, CPLB, GLRMC, GLRZJLB, GLRZJBH, GLRZJQSRQ, GLRZJJZRQ, GLRDH, TGRMC, TGRZJLB, TGRZJBH, TGRZJQSRQ, TGRZJJZRQ, TGRDH, CPBASJ, CPBABH, CPBAJG, CPTGMS, SLVIDEO, ZZSMHQZT, AREANO, BRNO, SALENO, SRXX, ZZJGDMQSRQ, FRDBZJQSRQ, JBRZJQSRQ, DSSWDJZ, DSSWDJZFZJG, DSSWDJZNJRQ, DSSWDJJZRQ, SWDJZQSRQ, DSSWDJZQSRQ, FJXX, QTZYXX, QTFXQHYLBXX, NSR, ZDKHH, CPDM, HFLYURL, SSGPDM, SSDD, FZZJLB, FZZJBH, FZZJJZRQ, FZZJDZ, DJRQ, DJSJ, SHRQ, SHSJ, JQRSHZT, JQRSHBZ, JQRSHRQ, JQRSHSJ, JQRSHJD, SCCWYY, HFFS, HFZXJG, HFZXBZ, HFSBCS, HFRWZT, ZJDZTB, WBYWLSH, WBQDBZ, A5, A5_KHLX, TZZFL, KHXJ, KHDCWJID, ZWXM FROM CIF.LCFXCKH
	25/08/01 15:06:47 WARN JDBCPushDown: Default partition inference expression hash(ID)
	25/08/01 15:06:47 INFO JDBCPushDown: compute slice function mod(ora_hash(ID),5)
	25/08/01 15:06:47 WARN JDBCPushDown: get new partition inference expression mod(ora_hash(ID),5)
	25/08/01 15:06:47 WARN JDBCPushDown: Remove invalid configuration items partitionColumn
	25/08/01 15:06:47 WARN JDBCPushDown: Remove invalid configuration items lowerBound
	25/08/01 15:06:47 WARN JDBCPushDown: Remove invalid configuration items upperBound
	25/08/01 15:06:47 WARN JDBCPushDown: Remove invalid configuration items numPartitions
	25/08/01 15:06:47 WARN JDBCPushDown: Remove invalid configuration items partitionColumn from properties
	25/08/01 15:06:47 WARN JDBCPushDown: Remove invalid configuration items lowerBound from properties
	25/08/01 15:06:47 WARN JDBCPushDown: Remove invalid configuration items upperBound from properties
	25/08/01 15:06:47 WARN JDBCPushDown: Remove invalid configuration items numPartitions from properties
	25/08/01 15:06:47 INFO JDBCPushDown: Calculate the partition list [mod(ora_hash(ID),5) = 0, mod(ora_hash(ID),5) = 1, mod(ora_hash(ID),5) = 2, mod(ora_hash(ID),5) = 3, mod(ora_hash(ID),5) = 4]
	root
	 |-- ID: decimal(16,0) (nullable = true)
	 |-- INSTID: decimal(16,0) (nullable = true)
	 |-- SQRQ: decimal(8,0) (nullable = true)
	 |-- SQSJ: string (nullable = true)
	 |-- KHFS: decimal(12,0) (nullable = true)
	 |-- KHZD: decimal(12,0) (nullable = true)
	 |-- JGBZ: decimal(12,0) (nullable = true)
	 |-- YWQQID: decimal(16,0) (nullable = true)
	 |-- YWDM: string (nullable = true)
	 |-- FQQD: decimal(12,0) (nullable = true)
	 |-- YYB: decimal(12,0) (nullable = true)
	 |-- GTKHH: string (nullable = true)
	 |-- KHLY: decimal(12,0) (nullable = true)
	 |-- KHXM: string (nullable = true)
	 |-- KHQC: string (nullable = true)
	 |-- DJR: decimal(16,0) (nullable = true)
	 |-- SHGY: decimal(16,0) (nullable = true)
	 |-- CZZD: string (nullable = true)
	 |-- KHXZR: decimal(16,0) (nullable = true)
	 |-- JZR: decimal(16,0) (nullable = true)
	 |-- QDBM: string (nullable = true)
	 |-- YXRY: string (nullable = true)
	 |-- DN: string (nullable = true)
	 |-- ZJLB: decimal(12,0) (nullable = true)
	 |-- ZJBH: string (nullable = true)
	 |-- XB: decimal(12,0) (nullable = true)
	 |-- CSRQ: decimal(8,0) (nullable = true)
	 |-- ZJYXQ: string (nullable = true)
	 |-- ZJDZ: string (nullable = true)
	 |-- ZJDZYB: string (nullable = true)
	 |-- ZJFZJG: string (nullable = true)
	 |-- ZJZP: string (nullable = true)
	 |-- ZJYZLY: decimal(12,0) (nullable = true)
	 |-- EDZ: decimal(38,0) (nullable = true)
	 |-- YYZZNJRQ: decimal(8,0) (nullable = true)
	 |-- DZ: string (nullable = true)
	 |-- YZBM: string (nullable = true)
	 |-- JTDZ: string (nullable = true)
	 |-- JTDZYB: string (nullable = true)
	 |-- SJ: string (nullable = true)
	 |-- DH: string (nullable = true)
	 |-- CZ: string (nullable = true)
	 |-- EMAIL: string (nullable = true)
	 |-- ZYDM: decimal(12,0) (nullable = true)
	 |-- HYZK: decimal(12,0) (nullable = true)
	 |-- XL: decimal(12,0) (nullable = true)
	 |-- GZDW: string (nullable = true)
	 |-- GZDWDZ: string (nullable = true)
	 |-- MZDM: decimal(12,0) (nullable = true)
	 |-- JG: string (nullable = true)
	 |-- GJ: decimal(12,0) (nullable = true)
	 |-- PROVINCE: string (nullable = true)
	 |-- CITY: string (nullable = true)
	 |-- SEC: string (nullable = true)
	 |-- TBSM: string (nullable = true)
	 |-- FXQHYLB: decimal(12,0) (nullable = true)
	 |-- XQFXDJ: decimal(12,0) (nullable = true)
	 |-- QYXZ: decimal(12,0) (nullable = true)
	 |-- HYDM: decimal(12,0) (nullable = true)
	 |-- FRLB: decimal(12,0) (nullable = true)
	 |-- YWMC: string (nullable = true)
	 |-- JGGSDH: string (nullable = true)
	 |-- JGZCDZ: string (nullable = true)
	 |-- JGZCRQ: decimal(8,0) (nullable = true)
	 |-- JGJYFW: string (nullable = true)
	 |-- JGZCZB: decimal(16,2) (nullable = true)
	 |-- JGZGB: decimal(12,0) (nullable = true)
	 |-- JGLTGB: decimal(12,0) (nullable = true)
	 |-- ZZJGDM: string (nullable = true)
	 |-- ZZJGDMYXQ: decimal(8,0) (nullable = true)
	 |-- ZZJGDMFZJG: string (nullable = true)
	 |-- ZZJGDMNJRQ: decimal(8,0) (nullable = true)
	 |-- SWDJZ: string (nullable = true)
	 |-- SWDJZYXQ: decimal(8,0) (nullable = true)
	 |-- SWDJZFZJG: string (nullable = true)
	 |-- SWDJZNJRQ: decimal(8,0) (nullable = true)
	 |-- FRDBXM: string (nullable = true)
	 |-- FRDBZJLB: decimal(12,0) (nullable = true)
	 |-- FRDBZJBH: string (nullable = true)
	 |-- FRDBZJYXQ: string (nullable = true)
	 |-- JBRXM: string (nullable = true)
	 |-- JBRZJLB: decimal(12,0) (nullable = true)
	 |-- JBRZJBH: string (nullable = true)
	 |-- JBRZJYXQ: string (nullable = true)
	 |-- JBRXB: decimal(12,0) (nullable = true)
	 |-- JBRDH: string (nullable = true)
	 |-- JBRSJ: string (nullable = true)
	 |-- BZ: string (nullable = true)
	 |-- KHQZ: decimal(16,0) (nullable = true)
	 |-- KHKH: string (nullable = true)
	 |-- WTFS: string (nullable = true)
	 |-- FWXM: string (nullable = true)
	 |-- FXJB: decimal(12,0) (nullable = true)
	 |-- FXCSNL: decimal(12,0) (nullable = true)
	 |-- GPFXCSNL: decimal(12,0) (nullable = true)
	 |-- SFTB: decimal(12,0) (nullable = true)
	 |-- JYMM: string (nullable = true)
	 |-- ZJMM: string (nullable = true)
	 |-- FWMM: string (nullable = true)
	 |-- SQJJZH: string (nullable = true)
	 |-- GDKH_SH: decimal(12,0) (nullable = true)
	 |-- GDDJ_SH: string (nullable = true)
	 |-- GDKH_HJ: decimal(12,0) (nullable = true)
	 |-- GDDJ_HJ: string (nullable = true)
	 |-- GDKH_HB: decimal(12,0) (nullable = true)
	 |-- GDDJ_HB: string (nullable = true)
	 |-- GDKH_SZ: decimal(12,0) (nullable = true)
	 |-- GDDJ_SZ: string (nullable = true)
	 |-- GDKH_SJ: decimal(12,0) (nullable = true)
	 |-- GDDJ_SJ: string (nullable = true)
	 |-- GDKH_SB: decimal(12,0) (nullable = true)
	 |-- GDDJ_SB: string (nullable = true)
	 |-- GDKH_TA: decimal(12,0) (nullable = true)
	 |-- GDDJ_TA: string (nullable = true)
	 |-- GDKH_TU: decimal(12,0) (nullable = true)
	 |-- GDDJ_TU: string (nullable = true)
	 |-- GDJYQX_SH: string (nullable = true)
	 |-- GDJYQX_HJ: string (nullable = true)
	 |-- GDJYQX_HB: string (nullable = true)
	 |-- GDJYQX_SZ: string (nullable = true)
	 |-- GDJYQX_SJ: string (nullable = true)
	 |-- GDJYQX_SB: string (nullable = true)
	 |-- GDJYQX_TA: string (nullable = true)
	 |-- GDJYQX_TU: string (nullable = true)
	 |-- SHZDJY: decimal(38,0) (nullable = true)
	 |-- HBZDJY: decimal(38,0) (nullable = true)
	 |-- SFWLFW: decimal(38,0) (nullable = true)
	 |-- WLFWMM: string (nullable = true)
	 |-- CYBKT: decimal(38,0) (nullable = true)
	 |-- CYBKTFS: decimal(12,0) (nullable = true)
	 |-- CGYHZH: string (nullable = true)
	 |-- CGYHMM: string (nullable = true)
	 |-- YHDM_USD: string (nullable = true)
	 |-- DJFS_USD: decimal(12,0) (nullable = true)
	 |-- YHZH_USD: string (nullable = true)
	 |-- YHMM_USD: string (nullable = true)
	 |-- YHDM_HKD: string (nullable = true)
	 |-- DJFS_HKD: decimal(12,0) (nullable = true)
	 |-- YHZH_HKD: string (nullable = true)
	 |-- YHMM_HKD: string (nullable = true)
	 |-- KHZP: string (nullable = true)
	 |-- KHSP: string (nullable = true)
	 |-- HFRQ: decimal(8,0) (nullable = true)
	 |-- HFSJ: string (nullable = true)
	 |-- HFGYDM: string (nullable = true)
	 |-- HFGYXM: string (nullable = true)
	 |-- HFJG: decimal(12,0) (nullable = true)
	 |-- HFJGSM: string (nullable = true)
	 |-- HFTJ: decimal(12,0) (nullable = true)
	 |-- SHZT: decimal(12,0) (nullable = true)
	 |-- STEP: decimal(12,0) (nullable = true)
	 |-- CLJGSM: string (nullable = true)
	 |-- FHYJ: string (nullable = true)
	 |-- WJID: decimal(12,0) (nullable = true)
	 |-- TMDAC: string (nullable = true)
	 |-- HFWJID: decimal(12,0) (nullable = true)
	 |-- HFTMDAC: string (nullable = true)
	 |-- IBY1: decimal(12,0) (nullable = true)
	 |-- IBY2: decimal(12,0) (nullable = true)
	 |-- IBY3: decimal(12,0) (nullable = true)
	 |-- CBY1: string (nullable = true)
	 |-- CBY2: string (nullable = true)
	 |-- CBY3: string (nullable = true)
	 |-- JGLB: decimal(12,0) (nullable = true)
	 |-- ZBSX: decimal(12,0) (nullable = true)
	 |-- GYSX: decimal(12,0) (nullable = true)
	 |-- JGWZDZ: string (nullable = true)
	 |-- ZZJGDMZJDZ: string (nullable = true)
	 |-- SPLX: decimal(12,0) (nullable = true)
	 |-- RLDBSBL: decimal(9,3) (nullable = true)
	 |-- RLGY: decimal(16,0) (nullable = true)
	 |-- YSBZ: decimal(12,0) (nullable = true)
	 |-- YSXX: string (nullable = true)
	 |-- CPDF: decimal(12,2) (nullable = true)
	 |-- YJZT: decimal(12,0) (nullable = true)
	 |-- SSSX: decimal(12,0) (nullable = true)
	 |-- JGZCBZ: decimal(12,0) (nullable = true)
	 |-- SMJJGLRBM: string (nullable = true)
	 |-- JZRQ: decimal(8,0) (nullable = true)
	 |-- KGGD: string (nullable = true)
	 |-- KGGDZJLB: decimal(12,0) (nullable = true)
	 |-- KGGDZJBH: string (nullable = true)
	 |-- KGGDZJQSRQ: decimal(8,0) (nullable = true)
	 |-- KGGDZJJZRQ: decimal(8,0) (nullable = true)
	 |-- SJKZR: string (nullable = true)
	 |-- SJSYR: string (nullable = true)
	 |-- ZW: string (nullable = true)
	 |-- BLCXJL: string (nullable = true)
	 |-- ZZSM: string (nullable = true)
	 |-- ZJQSRQ: string (nullable = true)
	 |-- SSJMSF: decimal(12,0) (nullable = true)
	 |-- SSJGLB: decimal(12,0) (nullable = true)
	 |-- KZRXM: string (nullable = true)
	 |-- KZRSSJMSF: decimal(12,0) (nullable = true)
	 |-- KZRCSRQ: string (nullable = true)
	 |-- NAME_X: string (nullable = true)
	 |-- NAME_M: string (nullable = true)
	 |-- JGMCE: string (nullable = true)
	 |-- JGDZ_GJ: string (nullable = true)
	 |-- JGDZ_SF: string (nullable = true)
	 |-- JGDZ_CS: string (nullable = true)
	 |-- JGDZ: string (nullable = true)
	 |-- JGDZE_GJ: string (nullable = true)
	 |-- JGDZE_SF: string (nullable = true)
	 |-- JGDZE_CS: string (nullable = true)
	 |-- JGDZE: string (nullable = true)
	 |-- XJDZ_GJ: string (nullable = true)
	 |-- XJDZ_SF: string (nullable = true)
	 |-- XJDZ_CS: string (nullable = true)
	 |-- XJDZ: string (nullable = true)
	 |-- XJDZE_GJ: string (nullable = true)
	 |-- XJDZE_SF: string (nullable = true)
	 |-- XJDZE_CS: string (nullable = true)
	 |-- XJDZE: string (nullable = true)
	 |-- CSD_GJ: string (nullable = true)
	 |-- CSD_SF: string (nullable = true)
	 |-- CSD_CS: string (nullable = true)
	 |-- CSD: string (nullable = true)
	 |-- CSDE_GJ: string (nullable = true)
	 |-- CSDE_SF: string (nullable = true)
	 |-- CSDE_CS: string (nullable = true)
	 |-- CSDE: string (nullable = true)
	 |-- SSJMG1: decimal(12,0) (nullable = true)
	 |-- NSRSBH1: string (nullable = true)
	 |-- SSJMG2: decimal(12,0) (nullable = true)
	 |-- NSRSBH2: string (nullable = true)
	 |-- SSJMG3: decimal(12,0) (nullable = true)
	 |-- NSRSBH3: string (nullable = true)
	 |-- WNSRSBHYY: decimal(12,0) (nullable = true)
	 |-- JGSSJMG1: decimal(12,0) (nullable = true)
	 |-- JGNSRSBH1: string (nullable = true)
	 |-- JGSSJMG2: decimal(12,0) (nullable = true)
	 |-- JGNSRSBH2: string (nullable = true)
	 |-- JGSSJMG3: decimal(12,0) (nullable = true)
	 |-- JGNSRSBH3: string (nullable = true)
	 |-- JGWNSRSBHYY: decimal(12,0) (nullable = true)
	 |-- RHZXSQ: decimal(12,0) (nullable = true)
	 |-- TDKHMD: string (nullable = true)
	 |-- JGWNSRSBHJTYY: string (nullable = true)
	 |-- WNSRSBHJTYY: string (nullable = true)
	 |-- ZZZSBH: string (nullable = true)
	 |-- SFWGSGLF: decimal(12,0) (nullable = true)
	 |-- ZJLY: decimal(12,0) (nullable = true)
	 |-- FZRXM: string (nullable = true)
	 |-- FZRZJLB: decimal(12,0) (nullable = true)
	 |-- FZRZJBH: string (nullable = true)
	 |-- FZRZJQSRQ: decimal(8,0) (nullable = true)
	 |-- FZRZJJZRQ: decimal(8,0) (nullable = true)
	 |-- CPCSGGBL: string (nullable = true)
	 |-- TDZRR: string (nullable = true)
	 |-- CPBZ: decimal(12,0) (nullable = true)
	 |-- CYRLB: decimal(12,0) (nullable = true)
	 |-- CPCLSJ: decimal(8,0) (nullable = true)
	 |-- CPDQRQ: decimal(8,0) (nullable = true)
	 |-- CPJG: decimal(12,0) (nullable = true)
	 |-- CPGM: decimal(22,4) (nullable = true)
	 |-- SYTZ: string (nullable = true)
	 |-- CPLB: decimal(12,0) (nullable = true)
	 |-- GLRMC: string (nullable = true)
	 |-- GLRZJLB: decimal(12,0) (nullable = true)
	 |-- GLRZJBH: string (nullable = true)
	 |-- GLRZJQSRQ: decimal(8,0) (nullable = true)
	 |-- GLRZJJZRQ: decimal(8,0) (nullable = true)
	 |-- GLRDH: string (nullable = true)
	 |-- TGRMC: string (nullable = true)
	 |-- TGRZJLB: decimal(12,0) (nullable = true)
	 |-- TGRZJBH: string (nullable = true)
	 |-- TGRZJQSRQ: decimal(8,0) (nullable = true)
	 |-- TGRZJJZRQ: decimal(8,0) (nullable = true)
	 |-- TGRDH: string (nullable = true)
	 |-- CPBASJ: decimal(8,0) (nullable = true)
	 |-- CPBABH: string (nullable = true)
	 |-- CPBAJG: string (nullable = true)
	 |-- CPTGMS: decimal(12,0) (nullable = true)
	 |-- SLVIDEO: string (nullable = true)
	 |-- ZZSMHQZT: decimal(12,0) (nullable = true)
	 |-- AREANO: string (nullable = true)
	 |-- BRNO: string (nullable = true)
	 |-- SALENO: string (nullable = true)
	 |-- SRXX: string (nullable = true)
	 |-- ZZJGDMQSRQ: decimal(8,0) (nullable = true)
	 |-- FRDBZJQSRQ: decimal(8,0) (nullable = true)
	 |-- JBRZJQSRQ: string (nullable = true)
	 |-- DSSWDJZ: string (nullable = true)
	 |-- DSSWDJZFZJG: string (nullable = true)
	 |-- DSSWDJZNJRQ: decimal(8,0) (nullable = true)
	 |-- DSSWDJJZRQ: decimal(8,0) (nullable = true)
	 |-- SWDJZQSRQ: decimal(8,0) (nullable = true)
	 |-- DSSWDJZQSRQ: decimal(8,0) (nullable = true)
	 |-- FJXX: string (nullable = true)
	 |-- QTZYXX: string (nullable = true)
	 |-- QTFXQHYLBXX: string (nullable = true)
	 |-- NSR: decimal(16,0) (nullable = true)
	 |-- ZDKHH: string (nullable = true)
	 |-- CPDM: string (nullable = true)
	 |-- HFLYURL: string (nullable = true)
	 |-- SSGPDM: string (nullable = true)
	 |-- SSDD: string (nullable = true)
	 |-- FZZJLB: decimal(12,0) (nullable = true)
	 |-- FZZJBH: string (nullable = true)
	 |-- FZZJJZRQ: decimal(8,0) (nullable = true)
	 |-- FZZJDZ: string (nullable = true)
	 |-- DJRQ: decimal(8,0) (nullable = true)
	 |-- DJSJ: string (nullable = true)
	 |-- SHRQ: decimal(8,0) (nullable = true)
	 |-- SHSJ: string (nullable = true)
	 |-- JQRSHZT: decimal(12,0) (nullable = true)
	 |-- JQRSHBZ: string (nullable = true)
	 |-- JQRSHRQ: decimal(8,0) (nullable = true)
	 |-- JQRSHSJ: string (nullable = true)
	 |-- JQRSHJD: string (nullable = true)
	 |-- SCCWYY: string (nullable = true)
	 |-- HFFS: decimal(12,0) (nullable = true)
	 |-- HFZXJG: decimal(12,0) (nullable = true)
	 |-- HFZXBZ: string (nullable = true)
	 |-- HFSBCS: decimal(4,0) (nullable = true)
	 |-- HFRWZT: decimal(12,0) (nullable = true)
	 |-- ZJDZTB: decimal(12,0) (nullable = true)
	 |-- WBYWLSH: decimal(16,0) (nullable = true)
	 |-- WBQDBZ: decimal(12,0) (nullable = true)
	 |-- A5: decimal(12,0) (nullable = true)
	 |-- A5_KHLX: decimal(12,0) (nullable = true)
	 |-- TZZFL: decimal(12,0) (nullable = true)
	 |-- KHXJ: decimal(12,0) (nullable = true)
	 |-- KHDCWJID: decimal(16,0) (nullable = true)
	 |-- ZWXM: string (nullable = true)
	
[INFO] 2025-08-01 15:06:49.699 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[69] -  -> 25/08/01 15:06:48 WARN package: Truncated the string representation of a plan since it was too large. This behavior can be adjusted by setting 'spark.sql.debug.maxToStringFields'.
[INFO] 2025-08-01 15:06:54.701 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[69] -  -> 25/08/01 15:06:53 WARN JdbcUtil: execute SELECT MAX(ID) as max_value,MIN(ID) as min_value FROM CIF.TYWQQ_CZLS
	25/08/01 15:06:53 WARN JdbcBatchSource: Get automatic inference information true
	25/08/01 15:06:53 WARN JdbcBatchSource: Find if partition column is of type string ID
	25/08/01 15:06:53 INFO JdbcBatchSource: Set Dynamic Partitioning Inference List Usage Parameters spark.app.datahub.jdbc.dynamic.partition.list
	25/08/01 15:06:53 INFO JdbcBatchSource: Override Dynamic Partitioning Inference Default Number of Partitions Usage Parameter spark.app.datahub.jdbc.default.numPartitions
	25/08/01 15:06:53 INFO JdbcBatchSource: Override default partition tile function use parameters spark.app.datahub.jdbc.default.splitFunc
	25/08/01 15:06:53 WARN JdbcBatchSource: Console entry of the above parameters is not global. Default Overrides default values only
	25/08/01 15:06:53 WARN JdbcBatchSource: Partition inference using user-defined types pushdown=true
	25/08/01 15:06:53 INFO JdbcBatchSource: Use user-defined query SQL SELECT ID, KHH, YWQQID, CZR, CZRQ, CZSJ, CZZD, CZLX, YWQQZT, BZSM, ACTION, JDMC, FLAG, HISTORYSTEPID, CZYS, ORGID, JDDM FROM CIF.TYWQQ_CZLS
	25/08/01 15:06:53 WARN JDBCPushDown: Default partition inference expression hash(ID)
	25/08/01 15:06:53 INFO JDBCPushDown: compute slice function mod(ora_hash(ID),5)
	25/08/01 15:06:53 WARN JDBCPushDown: get new partition inference expression mod(ora_hash(ID),5)
	25/08/01 15:06:53 WARN JDBCPushDown: Remove invalid configuration items partitionColumn
	25/08/01 15:06:53 WARN JDBCPushDown: Remove invalid configuration items lowerBound
	25/08/01 15:06:53 WARN JDBCPushDown: Remove invalid configuration items upperBound
	25/08/01 15:06:53 WARN JDBCPushDown: Remove invalid configuration items numPartitions
	25/08/01 15:06:53 WARN JDBCPushDown: Remove invalid configuration items partitionColumn from properties
	25/08/01 15:06:53 WARN JDBCPushDown: Remove invalid configuration items lowerBound from properties
	25/08/01 15:06:53 WARN JDBCPushDown: Remove invalid configuration items upperBound from properties
	25/08/01 15:06:53 WARN JDBCPushDown: Remove invalid configuration items numPartitions from properties
	25/08/01 15:06:53 INFO JDBCPushDown: Calculate the partition list [mod(ora_hash(ID),5) = 0, mod(ora_hash(ID),5) = 1, mod(ora_hash(ID),5) = 2, mod(ora_hash(ID),5) = 3, mod(ora_hash(ID),5) = 4]
	root
	 |-- ID: decimal(16,0) (nullable = true)
	 |-- KHH: string (nullable = true)
	 |-- YWQQID: decimal(16,0) (nullable = true)
	 |-- CZR: decimal(12,0) (nullable = true)
	 |-- CZRQ: decimal(8,0) (nullable = true)
	 |-- CZSJ: string (nullable = true)
	 |-- CZZD: string (nullable = true)
	 |-- CZLX: decimal(4,0) (nullable = true)
	 |-- YWQQZT: decimal(4,0) (nullable = true)
	 |-- BZSM: string (nullable = true)
	 |-- ACTION: string (nullable = true)
	 |-- JDMC: string (nullable = true)
	 |-- FLAG: decimal(38,10) (nullable = true)
	 |-- HISTORYSTEPID: decimal(38,10) (nullable = true)
	 |-- CZYS: string (nullable = true)
	 |-- ORGID: decimal(38,10) (nullable = true)
	 |-- JDDM: string (nullable = true)
	
	25/08/01 15:06:53 WARN JdbcUtil: execute SELECT MAX(ID) as max_value,MIN(ID) as min_value FROM DOAMP.TIC_ZBCS
	25/08/01 15:06:53 WARN JdbcBatchSource: Get automatic inference information true
	25/08/01 15:06:53 WARN JdbcBatchSource: Find if partition column is of type string ID
	25/08/01 15:06:53 INFO JdbcBatchSource: Set Dynamic Partitioning Inference List Usage Parameters spark.app.datahub.jdbc.dynamic.partition.list
	25/08/01 15:06:53 INFO JdbcBatchSource: Override Dynamic Partitioning Inference Default Number of Partitions Usage Parameter spark.app.datahub.jdbc.default.numPartitions
	25/08/01 15:06:53 INFO JdbcBatchSource: Override default partition tile function use parameters spark.app.datahub.jdbc.default.splitFunc
	25/08/01 15:06:53 WARN JdbcBatchSource: Console entry of the above parameters is not global. Default Overrides default values only
	25/08/01 15:06:53 WARN JdbcBatchSource: Partition inference using user-defined types pushdown=true
	25/08/01 15:06:53 INFO JdbcBatchSource: Use user-defined query SQL SELECT ID, FID, GRADE, TYPE, NAME, FDNCODE, IDX_CODE, IDX_CODE_SRC, IDX_NM, IDX_DISPLAY_NAME, IDX_CL, IDX_GRD, STATUS, DESCR, PRCS, DEPARTMENT, ZBLX, INDEX_FLAG FROM DOAMP.TIC_ZBCS
	25/08/01 15:06:53 WARN JDBCPushDown: Default partition inference expression hash(ID)
	25/08/01 15:06:53 INFO JDBCPushDown: compute slice function mod(ora_hash(ID),5)
	25/08/01 15:06:53 WARN JDBCPushDown: get new partition inference expression mod(ora_hash(ID),5)
	25/08/01 15:06:53 WARN JDBCPushDown: Remove invalid configuration items partitionColumn
	25/08/01 15:06:53 WARN JDBCPushDown: Remove invalid configuration items lowerBound
	25/08/01 15:06:53 WARN JDBCPushDown: Remove invalid configuration items upperBound
	25/08/01 15:06:53 WARN JDBCPushDown: Remove invalid configuration items numPartitions
	25/08/01 15:06:53 WARN JDBCPushDown: Remove invalid configuration items partitionColumn from properties
	25/08/01 15:06:53 WARN JDBCPushDown: Remove invalid configuration items lowerBound from properties
	25/08/01 15:06:53 WARN JDBCPushDown: Remove invalid configuration items upperBound from properties
	25/08/01 15:06:53 WARN JDBCPushDown: Remove invalid configuration items numPartitions from properties
	25/08/01 15:06:53 INFO JDBCPushDown: Calculate the partition list [mod(ora_hash(ID),5) = 0, mod(ora_hash(ID),5) = 1, mod(ora_hash(ID),5) = 2, mod(ora_hash(ID),5) = 3, mod(ora_hash(ID),5) = 4]
	root
	 |-- ID: decimal(16,0) (nullable = true)
	 |-- FID: decimal(12,0) (nullable = true)
	 |-- GRADE: decimal(12,0) (nullable = true)
	 |-- TYPE: decimal(12,0) (nullable = true)
	 |-- NAME: string (nullable = true)
	 |-- FDNCODE: string (nullable = true)
	 |-- IDX_CODE: string (nullable = true)
	 |-- IDX_CODE_SRC: string (nullable = true)
	 |-- IDX_NM: string (nullable = true)
	 |-- IDX_DISPLAY_NAME: string (nullable = true)
	 |-- IDX_CL: decimal(12,0) (nullable = true)
	 |-- IDX_GRD: decimal(12,0) (nullable = true)
	 |-- STATUS: decimal(12,0) (nullable = true)
	 |-- DESCR: string (nullable = true)
	 |-- PRCS: decimal(10,0) (nullable = true)
	 |-- DEPARTMENT: decimal(12,0) (nullable = true)
	 |-- ZBLX: decimal(12,0) (nullable = true)
	 |-- INDEX_FLAG: decimal(12,0) (nullable = true)
	
	25/08/01 15:06:54 WARN SimpleFunctionRegistry: The function uuid replaced a previously registered function.
	25/08/01 15:06:54 WARN BuiltInFunction: The current function only supports connecting to MySQL external data sources
	25/08/01 15:06:54 INFO BuiltInFunction: Register built-in functions sys_get_daydiff
	25/08/01 15:06:54 INFO BuiltInFunction: Register built-in functions sys_get_jyr_date
	25/08/01 15:06:54 INFO BuiltInFunction: Register built-in functions sys_get_new_yyb
	25/08/01 15:06:54 INFO BuiltInFunction: Register built-in functions sys_get_etl_tran_dicval
	25/08/01 15:06:54 INFO BuiltInFunction: Register built-in functions sys_get_etl_tran_bitdicval
	25/08/01 15:06:54 INFO BuiltInFunction: Register built-in functions sys_get_date
	25/08/01 15:06:54 INFO BuiltInFunction: Register built-in functions sys_get_hlcs
	25/08/01 15:06:54 INFO BuiltInFunction: Register built-in functions sys_get_paramvalue
	25/08/01 15:06:54 INFO BuiltInFunction: Register built-in functions sys_get_date_py
	25/08/01 15:06:54 INFO BuiltInFunction: Register built-in functions sys_get_etl_tran_zqlb_dicval
	25/08/01 15:06:54 INFO BuiltInFunction: Register built-in functions sys_get_etl_bsdirection
	25/08/01 15:06:54 INFO BuiltInFunction: Register built-in functions sys_get_jobid
	25/08/01 15:06:54 INFO SparkSession: 
	CREATE TABLE LCFXCKH_mapping (
	ID DECIMAL(16,0),
	INSTID DECIMAL(16,0),
	SQRQ DECIMAL(8,0),
	SQSJ VARCHAR(8),
	KHFS DECIMAL(12,0),
	KHZD DECIMAL(12,0),
	JGBZ DECIMAL(12,0),
	YWQQID DECIMAL(16,0),
	YWDM VARCHAR(5),
	FQQD DECIMAL(12,0),
	YYB DECIMAL(12,0),
	GTKHH VARCHAR(12),
	KHLY DECIMAL(12,0),
	KHXM VARCHAR(20),
	KHQC VARCHAR(120),
	DJR DECIMAL(16,0),
	SHGY DECIMAL(16,0),
	CZZD VARCHAR(256),
	KHXZR DECIMAL(16,0),
	JZR DECIMAL(16,0),
	QDBM VARCHAR(30),
	YXRY VARCHAR(30),
	DN VARCHAR(128),
	ZJLB DECIMAL(12,0),
	ZJBH VARCHAR(40),
	XB DECIMAL(12,0),
	CSRQ DECIMAL(8,0),
	ZJYXQ VARCHAR(10),
	ZJDZ VARCHAR(100),
	ZJDZYB VARCHAR(6),
	ZJFZJG VARCHAR(60),
	ZJZP VARCHAR(100),
	ZJYZLY DECIMAL(12,0),
	EDZ DECIMAL(38,0),
	YYZZNJRQ DECIMAL(8,0),
	DZ VARCHAR(100),
	YZBM VARCHAR(6),
	JTDZ VARCHAR(100),
	JTDZYB VARCHAR(6),
	SJ VARCHAR(30),
	DH VARCHAR(20),
	CZ VARCHAR(20),
	EMAIL VARCHAR(50),
	ZYDM DECIMAL(12,0),
	HYZK DECIMAL(12,0),
	XL DECIMAL(12,0),
	GZDW VARCHAR(60),
	GZDWDZ VARCHAR(100),
	MZDM DECIMAL(12,0),
	JG VARCHAR(20),
	GJ DECIMAL(12,0),
	PROVINCE VARCHAR(30),
	CITY VARCHAR(6),
	SEC VARCHAR(6),
	TBSM VARCHAR(255),
	FXQHYLB DECIMAL(12,0),
	XQFXDJ DECIMAL(12,0),
	QYXZ DECIMAL(12,0),
	HYDM DECIMAL(12,0),
	FRLB DECIMAL(12,0),
	YWMC VARCHAR(100),
	JGGSDH VARCHAR(20),
	JGZCDZ VARCHAR(100),
	JGZCRQ DECIMAL(8,0),
	JGJYFW VARCHAR(2000),
	JGZCZB DECIMAL(16,2),
	JGZGB DECIMAL(12,0),
	JGLTGB DECIMAL(12,0),
	ZZJGDM VARCHAR(16),
	ZZJGDMYXQ DECIMAL(8,0),
	ZZJGDMFZJG VARCHAR(60),
	ZZJGDMNJRQ DECIMAL(8,0),
	SWDJZ VARCHAR(60),
	SWDJZYXQ DECIMAL(8,0),
	SWDJZFZJG VARCHAR(60),
	SWDJZNJRQ DECIMAL(8,0),
	FRDBXM VARCHAR(40),
	FRDBZJLB DECIMAL(12,0),
	FRDBZJBH VARCHAR(30),
	FRDBZJYXQ VARCHAR(10),
	JBRXM VARCHAR(16),
	JBRZJLB DECIMAL(12,0),
	JBRZJBH VARCHAR(30),
	JBRZJYXQ VARCHAR(10),
	JBRXB DECIMAL(12,0),
	JBRDH VARCHAR(20),
	JBRSJ VARCHAR(20),
	BZ VARCHAR(300),
	KHQZ DECIMAL(16,0),
	KHKH VARCHAR(30),
	WTFS VARCHAR(300),
	FWXM VARCHAR(300),
	FXJB DECIMAL(12,0),
	FXCSNL DECIMAL(12,0),
	GPFXCSNL DECIMAL(12,0),
	SFTB DECIMAL(12,0),
	JYMM VARCHAR(100),
	ZJMM VARCHAR(100),
	FWMM VARCHAR(100),
	SQJJZH VARCHAR(1024),
	GDKH_SH DECIMAL(12,0),
	GDDJ_SH VARCHAR(10),
	GDKH_HJ DECIMAL(12,0),
	GDDJ_HJ VARCHAR(10),
	GDKH_HB DECIMAL(12,0),
	GDDJ_HB VARCHAR(10),
	GDKH_SZ DECIMAL(12,0),
	GDDJ_SZ VARCHAR(10),
	GDKH_SJ DECIMAL(12,0),
	GDDJ_SJ VARCHAR(10),
	GDKH_SB DECIMAL(12,0),
	GDDJ_SB VARCHAR(10),
	GDKH_TA DECIMAL(12,0),
	GDDJ_TA VARCHAR(10),
	GDKH_TU DECIMAL(12,0),
	GDDJ_TU VARCHAR(10),
	GDJYQX_SH VARCHAR(300),
	GDJYQX_HJ VARCHAR(300),
	GDJYQX_HB VARCHAR(300),
	GDJYQX_SZ VARCHAR(300),
	GDJYQX_SJ VARCHAR(300),
	GDJYQX_SB VARCHAR(300),
	GDJYQX_TA VARCHAR(300),
	GDJYQX_TU VARCHAR(300),
	SHZDJY DECIMAL(38,0),
	HBZDJY DECIMAL(38,0),
	SFWLFW DECIMAL(38,0),
	WLFWMM VARCHAR(100),
	CYBKT DECIMAL(38,0),
	CYBKTFS DECIMAL(12,0),
	CGYHZH VARCHAR(30),
	CGYHMM VARCHAR(100),
	YHDM_USD VARCHAR(4),
	DJFS_USD DECIMAL(12,0),
	YHZH_USD VARCHAR(30),
	YHMM_USD VARCHAR(30),
	YHDM_HKD VARCHAR(4),
	DJFS_HKD DECIMAL(12,0),
	YHZH_HKD VARCHAR(30),
	YHMM_HKD VARCHAR(30),
	KHZP VARCHAR(100),
	KHSP VARCHAR(100),
	HFRQ DECIMAL(8,0),
	HFSJ VARCHAR(8),
	HFGYDM VARCHAR(30),
	HFGYXM VARCHAR(30),
	HFJG DECIMAL(12,0),
	HFJGSM VARCHAR(500),
	HFTJ DECIMAL(12,0),
	SHZT DECIMAL(12,0),
	STEP DECIMAL(12,0),
	CLJGSM VARCHAR(255),
	FHYJ VARCHAR(200),
	WJID DECIMAL(12,0),
	TMDAC VARCHAR(1000),
	HFWJID DECIMAL(12,0),
	HFTMDAC VARCHAR(1000),
	IBY1 DECIMAL(12,0),
	IBY2 DECIMAL(12,0),
	IBY3 DECIMAL(12,0),
	CBY1 VARCHAR(30),
	CBY2 VARCHAR(30),
	CBY3 VARCHAR(30),
	JGLB DECIMAL(12,0),
	ZBSX DECIMAL(12,0),
	GYSX DECIMAL(12,0),
	JGWZDZ VARCHAR(50),
	ZZJGDMZJDZ VARCHAR(80),
	SPLX DECIMAL(12,0),
	RLDBSBL DECIMAL(9,3),
	RLGY DECIMAL(16,0),
	YSBZ DECIMAL(12,0),
	YSXX VARCHAR(2000),
	CPDF DECIMAL(12,2),
	YJZT DECIMAL(12,0),
	SSSX DECIMAL(12,0),
	JGZCBZ DECIMAL(12,0),
	SMJJGLRBM VARCHAR(10),
	JZRQ DECIMAL(8,0),
	KGGD VARCHAR(100),
	KGGDZJLB DECIMAL(12,0),
	KGGDZJBH VARCHAR(40),
	KGGDZJQSRQ DECIMAL(8,0),
	KGGDZJJZRQ DECIMAL(8,0),
	SJKZR VARCHAR(120),
	SJSYR VARCHAR(120),
	ZW VARCHAR(40),
	BLCXJL VARCHAR(300),
	ZZSM VARCHAR(1000),
	ZJQSRQ VARCHAR(10),
	SSJMSF DECIMAL(12,0),
	SSJGLB DECIMAL(12,0),
	KZRXM VARCHAR(120),
	KZRSSJMSF DECIMAL(12,0),
	KZRCSRQ VARCHAR(8),
	NAME_X VARCHAR(60),
	NAME_M VARCHAR(60),
	JGMCE VARCHAR(120),
	JGDZ_GJ VARCHAR(60),
	JGDZ_SF VARCHAR(60),
	JGDZ_CS VARCHAR(60),
	JGDZ VARCHAR(120),
	JGDZE_GJ VARCHAR(60),
	JGDZE_SF VARCHAR(60),
	JGDZE_CS VARCHAR(60),
	JGDZE VARCHAR(120),
	XJDZ_GJ VARCHAR(60),
	XJDZ_SF VARCHAR(60),
	XJDZ_CS VARCHAR(60),
	XJDZ VARCHAR(120),
	XJDZE_GJ VARCHAR(60),
	XJDZE_SF VARCHAR(60),
	XJDZE_CS VARCHAR(60),
	XJDZE VARCHAR(120),
	CSD_GJ VARCHAR(60),
	CSD_SF VARCHAR(60),
	CSD_CS VARCHAR(60),
	CSD VARCHAR(120),
	CSDE_GJ VARCHAR(60),
	CSDE_SF VARCHAR(60),
	CSDE_CS VARCHAR(60),
	CSDE VARCHAR(120),
	SSJMG1 DECIMAL(12,0),
	NSRSBH1 VARCHAR(40),
	SSJMG2 DECIMAL(12,0),
	NSRSBH2 VARCHAR(40),
	SSJMG3 DECIMAL(12,0),
	NSRSBH3 VARCHAR(40),
	WNSRSBHYY DECIMAL(12,0),
	JGSSJMG1 DECIMAL(12,0),
	JGNSRSBH1 VARCHAR(40),
	JGSSJMG2 DECIMAL(12,0),
	JGNSRSBH2 VARCHAR(40),
	JGSSJMG3 DECIMAL(12,0),
	JGNSRSBH3 VARCHAR(40),
	JGWNSRSBHYY DECIMAL(12,0),
	RHZXSQ DECIMAL(12,0),
	TDKHMD VARCHAR(600),
	JGWNSRSBHJTYY VARCHAR(255),
	WNSRSBHJTYY VARCHAR(255),
	ZZZSBH VARCHAR(100),
	SFWGSGLF DECIMAL(12,0),
	ZJLY DECIMAL(12,0),
	FZRXM VARCHAR(60),
	FZRZJLB DECIMAL(12,0),
	FZRZJBH VARCHAR(40),
	FZRZJQSRQ DECIMAL(8,0),
	FZRZJJZRQ DECIMAL(8,0),
	CPCSGGBL VARCHAR(20),
	TDZRR VARCHAR(30),
	CPBZ DECIMAL(12,0),
	CYRLB DECIMAL(12,0),
	CPCLSJ DECIMAL(8,0),
	CPDQRQ DECIMAL(8,0),
	CPJG DECIMAL(12,0),
	CPGM DECIMAL(22,4),
	SYTZ VARCHAR(255),
	CPLB DECIMAL(12,0),
	GLRMC VARCHAR(120),
	GLRZJLB DECIMAL(12,0),
	GLRZJBH VARCHAR(40),
	GLRZJQSRQ DECIMAL(8,0),
	GLRZJJZRQ DECIMAL(8,0),
	GLRDH VARCHAR(20),
	TGRMC VARCHAR(120),
	TGRZJLB DECIMAL(12,0),
	TGRZJBH VARCHAR(40),
	TGRZJQSRQ DECIMAL(8,0),
	TGRZJJZRQ DECIMAL(8,0),
	TGRDH VARCHAR(20),
	CPBASJ DECIMAL(8,0),
	CPBABH VARCHAR(40),
	CPBAJG VARCHAR(120),
	CPTGMS DECIMAL(12,0),
	SLVIDEO VARCHAR(200),
	ZZSMHQZT DECIMAL(12,0),
	AREANO VARCHAR(30),
	BRNO VARCHAR(30),
	SALENO VARCHAR(30),
	SRXX VARCHAR(1000),
	ZZJGDMQSRQ DECIMAL(8,0),
	FRDBZJQSRQ DECIMAL(8,0),
	JBRZJQSRQ VARCHAR(10),
	DSSWDJZ VARCHAR(60),
	DSSWDJZFZJG VARCHAR(60),
	DSSWDJZNJRQ DECIMAL(8,0),
	DSSWDJJZRQ DECIMAL(8,0),
	SWDJZQSRQ DECIMAL(8,0),
	DSSWDJZQSRQ DECIMAL(8,0),
	FJXX VARCHAR(1000),
	QTZYXX VARCHAR(10),
	QTFXQHYLBXX VARCHAR(255),
	NSR DECIMAL(16,0),
	ZDKHH VARCHAR(12),
	CPDM VARCHAR(25),
	HFLYURL VARCHAR(200),
	SSGPDM VARCHAR(10),
	SSDD VARCHAR(30),
	FZZJLB DECIMAL(12,0),
	FZZJBH VARCHAR(40),
	FZZJJZRQ DECIMAL(8,0),
	FZZJDZ VARCHAR(80),
	DJRQ DECIMAL(8,0),
	DJSJ VARCHAR(8),
	SHRQ DECIMAL(8,0),
	SHSJ VARCHAR(8),
	JQRSHZT DECIMAL(12,0),
	JQRSHBZ VARCHAR(2000),
	JQRSHRQ DECIMAL(8,0),
	JQRSHSJ VARCHAR(8),
	JQRSHJD VARCHAR(100),
	SCCWYY STRING,
	HFFS DECIMAL(12,0),
	HFZXJG DECIMAL(12,0),
	HFZXBZ VARCHAR(200),
	HFSBCS DECIMAL(4,0),
	HFRWZT DECIMAL(12,0),
	ZJDZTB DECIMAL(12,0),
	WBYWLSH DECIMAL(16,0),
	WBQDBZ DECIMAL(12,0),
	A5 DECIMAL(12,0),
	A5_KHLX DECIMAL(12,0),
	TZZFL DECIMAL(12,0),
	KHXJ DECIMAL(12,0),
	KHDCWJID DECIMAL(16,0),
	ZWXM VARCHAR(30),
	PRIMARY KEY (ID) NOT ENFORCED
	) WITH (
	'connector' = 'jdbc',
	'type' = 'source',
	'url' = '*******************************************',
	'username' = 'cif',
	'password' = 'yY12dvqR6cVNYJXfNUeuqJZecw3vumuc',
	'table-name' = 'CIF.LCFXCKH'
	);
	25/08/01 15:06:54 INFO SparkSession: 
	CREATE TABLE TYWQQ_CZLS_mapping (
	ID DECIMAL(16,0),
	KHH VARCHAR(20),
	YWQQID DECIMAL(16,0),
	CZR DECIMAL(12,0),
	CZRQ DECIMAL(8,0),
	CZSJ VARCHAR(8),
	CZZD VARCHAR(256),
	CZLX DECIMAL(4,0),
	YWQQZT DECIMAL(4,0),
	BZSM VARCHAR(512),
	ACTION VARCHAR(30),
	JDMC VARCHAR(100),
	FLAG DECIMAL(38,18),
	HISTORYSTEPID DECIMAL(38,18),
	CZYS VARCHAR(4000),
	ORGID DECIMAL(38,18),
	JDDM VARCHAR(30),
	PRIMARY KEY (ID) NOT ENFORCED
	) WITH (
	'connector' = 'jdbc',
	'type' = 'source',
	'url' = '*******************************************',
	'username' = 'cif',
	'password' = 'yY12dvqR6cVNYJXfNUeuqJZecw3vumuc',
	'table-name' = 'CIF.TYWQQ_CZLS'
	);
	25/08/01 15:06:54 INFO SparkSession: 
	CREATE TABLE TIC_ZBCS_mapping (
	ID STRING,
	FID STRING,
	GRADE STRING,
	TYPE STRING,
	NAME VARCHAR(200),
	FDNCODE VARCHAR(300),
	IDX_CODE VARCHAR(30),
	IDX_CODE_SRC VARCHAR(30),
	IDX_NM VARCHAR(200),
	IDX_DISPLAY_NAME VARCHAR(100),
	IDX_CL STRING,
	IDX_GRD STRING,
	STATUS STRING,
	DESCR VARCHAR(1000),
	PRCS STRING,
	DEPARTMENT STRING,
	ZBLX STRING,
	INDEX_FLAG STRING,
	PRIMARY KEY (ID) NOT ENFORCED
	) WITH (
	'connector' = 'jdbc',
	'type' = 'source',
	'url' = 'jdbc:dm://**************:5236',
	'username' = 'doamp',
	'password' = 'oxTq6MIrcGVGDVFvcAUBsLS/M01vElzZ',
	'table-name' = 'DOAMP.TIC_ZBCS'
	);
	25/08/01 15:06:54 INFO SparkSession: 
	CREATE TABLE TIC_YXZB_mapping (
	RQ DECIMAL(8,0),
	IDX_ID DECIMAL(16,0),
	IDX_CODE VARCHAR(30),
	RESULT DECIMAL(20,4),
	CYCLE DECIMAL(2,0),
	PRIMARY KEY (RQ,IDX_ID) NOT ENFORCED
	) WITH (
	'connector' = 'jdbc',
	'type' = 'sink',
	'url' = 'jdbc:dm://**************:5236',
	'username' = 'doamp',
	'password' = 'oxTq6MIrcGVGDVFvcAUBsLS/M01vElzZ',
	'table-name' = 'DOAMP.TIC_YXZB'
	);
	25/08/01 15:06:54 INFO SparkSession: 
	CREATE TABLE TIC_YGZB_mapping (
	RQ DECIMAL(8,0),
	YG VARCHAR(30),
	IDX_ID DECIMAL(16,0),
	IDX_CODE VARCHAR(30),
	RESULT DECIMAL(20,4),
	CYCLE DECIMAL(2,0),
	PRIMARY KEY (RQ,YG,IDX_ID) NOT ENFORCED
	) WITH (
	'connector' = 'jdbc',
	'type' = 'sink',
	'url' = 'jdbc:dm://**************:5236',
	'username' = 'doamp',
	'password' = 'oxTq6MIrcGVGDVFvcAUBsLS/M01vElzZ',
	'table-name' = 'DOAMP.TIC_YGZB'
	);
	25/08/01 15:06:54 WARN SparkSession: set RQ=20250801;
	25/08/01 15:06:54 INFO SparkSession: 
	create temporary view wskh_base as select
	        ywqqid,
	        sqrq,
	        sqsj,
	        step,
	        khfs 
	    from
	        LCFXCKH_mapping 
	    where
	        sqrq = ${RQ} 
	        and step in (
	            1,2,3,5,6,99
	        ) 
	        and khfs in (
	            3,5
	        );
	25/08/01 15:06:54 INFO SparkSession: Logical interpretation plan:
	'CreateViewStatement [wskh_base], select ywqqid, sqrq, sqsj, step, khfs
	from LCFXCKH_mapping
	where sqrq = 20250801 and step in (1,2,3,5,6,99) and khfs in (3,5), false, false, LocalTempView
	+- 'Project ['ywqqid, 'sqrq, 'sqsj, 'step, 'khfs]
	   +- 'Filter ((('sqrq = 20250801) AND 'step IN (1,2,3,5,6,99)) AND 'khfs IN (3,5))
	      +- 'UnresolvedRelation [LCFXCKH_mapping], [], false
	
	25/08/01 15:06:54 WARN SparkSession: Spark SQL dataset syntax checking query syntax checking 
	create temporary view wskh_base as select
	        ywqqid,
	        sqrq,
	        sqsj,
	        step,
	        khfs 
	    from
	        LCFXCKH_mapping 
	    where
	        sqrq = ${RQ} 
	        and step in (
	            1,2,3,5,6,99
	        ) 
	        and khfs in (
	            3,5
	        )
[INFO] 2025-08-01 15:06:55.706 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[69] -  -> ++
	||
	++
	++
	
	25/08/01 15:06:54 INFO SparkSession: 
	create temporary view wskh_sh_ops as select
	        ls.ywqqid,
	        ls.czrq,
	        ls.czsj,
	        ls.czr,
	        ls.jdmc,
	        row_number() over(partition 
	    by
	        ls.ywqqid 
	    order by
	        ls.id) as rn 
	    from
	        TYWQQ_CZLS_mapping ls 
	    join
	        wskh_base wb 
	            on ls.ywqqid = wb.ywqqid 
	    where
	        (
	            instr(ls.jdmc,'非现初审') > 0 
	            or instr(ls.jdmc,'非现终审') > 0
	        ) 
	        and ls.czrq = ${RQ};
	25/08/01 15:06:55 INFO SparkSession: Logical interpretation plan:
	'CreateViewStatement [wskh_sh_ops], select
	ls.ywqqid, ls.czrq, ls.czsj, ls.czr, ls.jdmc,
	row_number() over(partition by ls.ywqqid order by ls.id) as rn
	from TYWQQ_CZLS_mapping ls
	join wskh_base wb on ls.ywqqid = wb.ywqqid
	where (instr(ls.jdmc,'非现初审') > 0 or instr(ls.jdmc,'非现终审') > 0)
	and ls.czrq = 20250801, false, false, LocalTempView
	+- 'Project ['ls.ywqqid, 'ls.czrq, 'ls.czsj, 'ls.czr, 'ls.jdmc, 'row_number() windowspecdefinition('ls.ywqqid, 'ls.id ASC NULLS FIRST, unspecifiedframe$()) AS rn#1502]
	   +- 'Filter ((('instr('ls.jdmc, 非现初审) > 0) OR ('instr('ls.jdmc, 非现终审) > 0)) AND ('ls.czrq = 20250801))
	      +- 'Join Inner, ('ls.ywqqid = 'wb.ywqqid)
	         :- 'SubqueryAlias ls
	         :  +- 'UnresolvedRelation [TYWQQ_CZLS_mapping], [], false
	         +- 'SubqueryAlias wb
	            +- 'UnresolvedRelation [wskh_base], [], false
	
	25/08/01 15:06:55 WARN SparkSession: Spark SQL dataset syntax checking query syntax checking 
	create temporary view wskh_sh_ops as select
	        ls.ywqqid,
	        ls.czrq,
	        ls.czsj,
	        ls.czr,
	        ls.jdmc,
	        row_number() over(partition 
	    by
	        ls.ywqqid 
	    order by
	        ls.id) as rn 
	    from
	        TYWQQ_CZLS_mapping ls 
	    join
	        wskh_base wb 
	            on ls.ywqqid = wb.ywqqid 
	    where
	        (
	            instr(ls.jdmc,'非现初审') > 0 
	            or instr(ls.jdmc,'非现终审') > 0
	        ) 
	        and ls.czrq = ${RQ}
	++
	||
	++
	++
	
	25/08/01 15:06:55 INFO SparkSession: 
	create temporary view wskh_fh_ops as select
	        ls.ywqqid,
	        ls.czrq,
	        ls.czsj,
	        ls.czr,
	        ls.jdmc,
	        row_number() over(partition 
	    by
	        ls.ywqqid 
	    order by
	        ls.id) as rn 
	    from
	        TYWQQ_CZLS_mapping ls 
	    join
	        wskh_base wb 
	            on ls.ywqqid = wb.ywqqid 
	    where
	        instr(ls.jdmc,'非现复审') > 0 
	        and ls.czrq = ${RQ};
	25/08/01 15:06:55 INFO SparkSession: Logical interpretation plan:
	'CreateViewStatement [wskh_fh_ops], select
	ls.ywqqid, ls.czrq, ls.czsj, ls.czr, ls.jdmc,
	row_number() over(partition by ls.ywqqid order by ls.id) as rn
	from TYWQQ_CZLS_mapping ls
	join wskh_base wb on ls.ywqqid = wb.ywqqid
	where instr(ls.jdmc,'非现复审') > 0
	and ls.czrq = 20250801, false, false, LocalTempView
	+- 'Project ['ls.ywqqid, 'ls.czrq, 'ls.czsj, 'ls.czr, 'ls.jdmc, 'row_number() windowspecdefinition('ls.ywqqid, 'ls.id ASC NULLS FIRST, unspecifiedframe$()) AS rn#1511]
	   +- 'Filter (('instr('ls.jdmc, 非现复审) > 0) AND ('ls.czrq = 20250801))
	      +- 'Join Inner, ('ls.ywqqid = 'wb.ywqqid)
	         :- 'SubqueryAlias ls
	         :  +- 'UnresolvedRelation [TYWQQ_CZLS_mapping], [], false
	         +- 'SubqueryAlias wb
	            +- 'UnresolvedRelation [wskh_base], [], false
	
	25/08/01 15:06:55 WARN SparkSession: Spark SQL dataset syntax checking query syntax checking 
	create temporary view wskh_fh_ops as select
	        ls.ywqqid,
	        ls.czrq,
	        ls.czsj,
	        ls.czr,
	        ls.jdmc,
	        row_number() over(partition 
	    by
	        ls.ywqqid 
	    order by
	        ls.id) as rn 
	    from
	        TYWQQ_CZLS_mapping ls 
	    join
	        wskh_base wb 
	            on ls.ywqqid = wb.ywqqid 
	    where
	        instr(ls.jdmc,'非现复审') > 0 
	        and ls.czrq = ${RQ}
	++
	||
	++
	++
	
	25/08/01 15:06:55 INFO SparkSession: 
	create temporary view wskh_dh_time as select
	        wb.ywqqid,
	        sh.czr,
	        round((unix_timestamp(concat(cast(sh.czrq as string),
	        ' ',
	        sh.czsj),
	        'yyyyMMdd HH:mm:ss') - unix_timestamp(concat(cast(wb.sqrq as string),
	        ' ',
	        wb.sqsj),
	        'yyyyMMdd HH:mm:ss')) / 60.0,
	        2) as dh_minutes 
	    from
	        wskh_base wb 
	    join
	        (
	            select
	                ywqqid,
	                czrq,
	                czsj,
	                czr,
	                row_number() over(partition 
	            by
	                ywqqid 
	            order by
	                id) as rn 
	            from
	                TYWQQ_CZLS_mapping 
	            where
	                (
	                    instr(jdmc,'非现初审') > 0 
	                    or instr(jdmc,'非现终审') > 0
	                ) 
	                and czrq = ${RQ} 
	        ) sh 
	            on wb.ywqqid = sh.ywqqid 
	            and sh.rn = 1;
	25/08/01 15:06:55 INFO SparkSession: Logical interpretation plan:
	'CreateViewStatement [wskh_dh_time], select
	wb.ywqqid,
	sh.czr,
	round((unix_timestamp(concat(cast(sh.czrq as string), ' ', sh.czsj), 'yyyyMMdd HH:mm:ss') -
	unix_timestamp(concat(cast(wb.sqrq as string), ' ', wb.sqsj), 'yyyyMMdd HH:mm:ss')) / 60.0, 2) as dh_minutes
	from wskh_base wb
	join (
	select ywqqid, czrq, czsj, czr,
	row_number() over(partition by ywqqid order by id) as rn
	from TYWQQ_CZLS_mapping
	where (instr(jdmc,'非现初审') > 0 or instr(jdmc,'非现终审') > 0)
	and czrq = 20250801
	) sh on wb.ywqqid = sh.ywqqid and sh.rn = 1, false, false, LocalTempView
	+- 'Project ['wb.ywqqid, 'sh.czr, 'round((('unix_timestamp('concat(cast('sh.czrq as string),  , 'sh.czsj), yyyyMMdd HH:mm:ss) - 'unix_timestamp('concat(cast('wb.sqrq as string),  , 'wb.sqsj), yyyyMMdd HH:mm:ss)) / 60.0), 2) AS dh_minutes#1521]
	   +- 'Join Inner, (('wb.ywqqid = 'sh.ywqqid) AND ('sh.rn = 1))
	      :- 'SubqueryAlias wb
	      :  +- 'UnresolvedRelation [wskh_base], [], false
	      +- 'SubqueryAlias sh
	         +- 'Project ['ywqqid, 'czrq, 'czsj, 'czr, 'row_number() windowspecdefinition('ywqqid, 'id ASC NULLS FIRST, unspecifiedframe$()) AS rn#1520]
	            +- 'Filter ((('instr('jdmc, 非现初审) > 0) OR ('instr('jdmc, 非现终审) > 0)) AND ('czrq = 20250801))
	               +- 'UnresolvedRelation [TYWQQ_CZLS_mapping], [], false
	
	25/08/01 15:06:55 WARN SparkSession: Spark SQL dataset syntax checking query syntax checking 
	create temporary view wskh_dh_time as select
	        wb.ywqqid,
	        sh.czr,
	        round((unix_timestamp(concat(cast(sh.czrq as string),
	        ' ',
	        sh.czsj),
	        'yyyyMMdd HH:mm:ss') - unix_timestamp(concat(cast(wb.sqrq as string),
	        ' ',
	        wb.sqsj),
	        'yyyyMMdd HH:mm:ss')) / 60.0,
	        2) as dh_minutes 
	    from
	        wskh_base wb 
	    join
	        (
	            select
	                ywqqid,
	                czrq,
	                czsj,
	                czr,
	                row_number() over(partition 
	            by
	                ywqqid 
	            order by
	                id) as rn 
	            from
	                TYWQQ_CZLS_mapping 
	            where
	                (
	                    instr(jdmc,'非现初审') > 0 
	                    or instr(jdmc,'非现终审') > 0
	                ) 
	                and czrq = ${RQ} 
	        ) sh 
	            on wb.ywqqid = sh.ywqqid 
	            and sh.rn = 1
	++
	||
	++
	++
	
	25/08/01 15:06:55 INFO SparkSession: 
	create temporary view wskh_sh_time as select
	        start_op.ywqqid,
	        start_op.czr,
	        round((unix_timestamp(concat(cast(end_op.czrq as string),
	        ' ',
	        end_op.czsj),
	        'yyyyMMdd HH:mm:ss') - unix_timestamp(concat(cast(start_op.czrq as string),
	        ' ',
	        start_op.czsj),
	        'yyyyMMdd HH:mm:ss')) / 60.0,
	        2) as sh_minutes 
	    from
	        ( select
	            ywqqid,
	            czrq,
	            czsj,
	            czr,
	            row_number() over(partition 
	        by
	            ywqqid 
	        order by
	            id) as rn 
	        from
	            TYWQQ_CZLS_mapping 
	        where
	            czlx = '201'   
	            and (
	                instr(jdmc,'非现初审') > 0 
	                or instr(jdmc,'非现终审') > 0
	            ) 
	            and czrq = ${RQ} ) start_op 
	    join
	        (
	            select
	                ywqqid,
	                czrq,
	                czsj,
	                czr,
	                row_number() over(partition 
	            by
	                ywqqid 
	            order by
	                id desc) as rn 
	            from
	                TYWQQ_CZLS_mapping 
	            where
	                czlx in (
	                    '301', '302'
	                )   
	                and (
	                    instr(jdmc,'非现初审') > 0 
	                    or instr(jdmc,'非现终审') > 0
	                ) 
	                and czrq = ${RQ} 
	        ) end_op 
	            on start_op.ywqqid = end_op.ywqqid 
	            and start_op.czr = end_op.czr 
	            and start_op.rn = 1 
	            and end_op.rn = 1;
	25/08/01 15:06:55 INFO SparkSession: Logical interpretation plan:
	'CreateViewStatement [wskh_sh_time], select
	start_op.ywqqid,
	start_op.czr,
	round((unix_timestamp(concat(cast(end_op.czrq as string), ' ', end_op.czsj), 'yyyyMMdd HH:mm:ss') -
	unix_timestamp(concat(cast(start_op.czrq as string), ' ', start_op.czsj), 'yyyyMMdd HH:mm:ss')) / 60.0, 2) as sh_minutes
	from (
	select ywqqid, czrq, czsj, czr,
	row_number() over(partition by ywqqid order by id) as rn
	from TYWQQ_CZLS_mapping
	where czlx = '201'  
	and (instr(jdmc,'非现初审') > 0 or instr(jdmc,'非现终审') > 0)
	and czrq = 20250801
	) start_op
	join (
	select ywqqid, czrq, czsj, czr,
	row_number() over(partition by ywqqid order by id desc) as rn
	from TYWQQ_CZLS_mapping
	where czlx in ('301', '302')  
	and (instr(jdmc,'非现初审') > 0 or instr(jdmc,'非现终审') > 0)
	and czrq = 20250801
	) end_op on start_op.ywqqid = end_op.ywqqid
	and start_op.czr = end_op.czr
	and start_op.rn = 1
	and end_op.rn = 1, false, false, LocalTempView
	+- 'Project ['start_op.ywqqid, 'start_op.czr, 'round((('unix_timestamp('concat(cast('end_op.czrq as string),  , 'end_op.czsj), yyyyMMdd HH:mm:ss) - 'unix_timestamp('concat(cast('start_op.czrq as string),  , 'start_op.czsj), yyyyMMdd HH:mm:ss)) / 60.0), 2) AS sh_minutes#1533]
	   +- 'Join Inner, ((('start_op.ywqqid = 'end_op.ywqqid) AND ('start_op.czr = 'end_op.czr)) AND (('start_op.rn = 1) AND ('end_op.rn = 1)))
	      :- 'SubqueryAlias start_op
	      :  +- 'Project ['ywqqid, 'czrq, 'czsj, 'czr, 'row_number() windowspecdefinition('ywqqid, 'id ASC NULLS FIRST, unspecifiedframe$()) AS rn#1531]
	      :     +- 'Filter ((('czlx = 201) AND (('instr('jdmc, 非现初审) > 0) OR ('instr('jdmc, 非现终审) > 0))) AND ('czrq = 20250801))
	      :        +- 'UnresolvedRelation [TYWQQ_CZLS_mapping], [], false
	      +- 'SubqueryAlias end_op
	         +- 'Project ['ywqqid, 'czrq, 'czsj, 'czr, 'row_number() windowspecdefinition('ywqqid, 'id DESC NULLS LAST, unspecifiedframe$()) AS rn#1532]
	            +- 'Filter (('czlx IN (301,302) AND (('instr('jdmc, 非现初审) > 0) OR ('instr('jdmc, 非现终审) > 0))) AND ('czrq = 20250801))
	               +- 'UnresolvedRelation [TYWQQ_CZLS_mapping], [], false
	
	25/08/01 15:06:55 WARN SparkSession: Spark SQL dataset syntax checking query syntax checking 
	create temporary view wskh_sh_time as select
	        start_op.ywqqid,
	        start_op.czr,
	        round((unix_timestamp(concat(cast(end_op.czrq as string),
	        ' ',
	        end_op.czsj),
	        'yyyyMMdd HH:mm:ss') - unix_timestamp(concat(cast(start_op.czrq as string),
	        ' ',
	        start_op.czsj),
	        'yyyyMMdd HH:mm:ss')) / 60.0,
	        2) as sh_minutes 
	    from
	        ( select
	            ywqqid,
	            czrq,
	            czsj,
	            czr,
	            row_number() over(partition 
	        by
	            ywqqid 
	        order by
	            id) as rn 
	        from
	            TYWQQ_CZLS_mapping 
	        where
	            czlx = '201'   
	            and (
	                instr(jdmc,'非现初审') > 0 
	                or instr(jdmc,'非现终审') > 0
	            ) 
	            and czrq = ${RQ} ) start_op 
	    join
	        (
	            select
	                ywqqid,
	                czrq,
	                czsj,
	                czr,
	                row_number() over(partition 
	            by
	                ywqqid 
	            order by
	                id desc) as rn 
	            from
	                TYWQQ_CZLS_mapping 
	            where
	                czlx in (
	                    '301', '302'
	                )   
	                and (
	                    instr(jdmc,'非现初审') > 0 
	                    or instr(jdmc,'非现终审') > 0
	                ) 
	                and czrq = ${RQ} 
	        ) end_op 
	            on start_op.ywqqid = end_op.ywqqid 
	            and start_op.czr = end_op.czr 
	            and start_op.rn = 1 
	            and end_op.rn = 1
[INFO] 2025-08-01 15:06:56.710 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[69] -  -> ++
	||
	++
	++
	
	25/08/01 15:06:55 INFO SparkSession: 
	create temporary view wskh_fh_time as select
	        start_op.ywqqid,
	        start_op.czr,
	        round((unix_timestamp(concat(cast(end_op.czrq as string),
	        ' ',
	        end_op.czsj),
	        'yyyyMMdd HH:mm:ss') - unix_timestamp(concat(cast(start_op.czrq as string),
	        ' ',
	        start_op.czsj),
	        'yyyyMMdd HH:mm:ss')) / 60.0,
	        2) as fh_minutes 
	    from
	        ( select
	            ywqqid,
	            czrq,
	            czsj,
	            czr,
	            row_number() over(partition 
	        by
	            ywqqid 
	        order by
	            id) as rn 
	        from
	            TYWQQ_CZLS_mapping 
	        where
	            czlx = '201'   
	            and instr(jdmc,'非现复审') > 0 
	            and czrq = ${RQ} ) start_op 
	    join
	        (
	            select
	                ywqqid,
	                czrq,
	                czsj,
	                czr,
	                row_number() over(partition 
	            by
	                ywqqid 
	            order by
	                id desc) as rn 
	            from
	                TYWQQ_CZLS_mapping 
	            where
	                czlx in (
	                    '301', '302'
	                )   
	                and instr(jdmc,'非现复审') > 0 
	                and czrq = ${RQ} 
	        ) end_op 
	            on start_op.ywqqid = end_op.ywqqid 
	            and start_op.czr = end_op.czr 
	            and start_op.rn = 1 
	            and end_op.rn = 1;
	25/08/01 15:06:55 INFO SparkSession: Logical interpretation plan:
	'CreateViewStatement [wskh_fh_time], select
	start_op.ywqqid,
	start_op.czr,
	round((unix_timestamp(concat(cast(end_op.czrq as string), ' ', end_op.czsj), 'yyyyMMdd HH:mm:ss') -
	unix_timestamp(concat(cast(start_op.czrq as string), ' ', start_op.czsj), 'yyyyMMdd HH:mm:ss')) / 60.0, 2) as fh_minutes
	from (
	select ywqqid, czrq, czsj, czr,
	row_number() over(partition by ywqqid order by id) as rn
	from TYWQQ_CZLS_mapping
	where czlx = '201'  
	and instr(jdmc,'非现复审') > 0
	and czrq = 20250801
	) start_op
	join (
	select ywqqid, czrq, czsj, czr,
	row_number() over(partition by ywqqid order by id desc) as rn
	from TYWQQ_CZLS_mapping
	where czlx in ('301', '302')  
	and instr(jdmc,'非现复审') > 0
	and czrq = 20250801
	) end_op on start_op.ywqqid = end_op.ywqqid
	and start_op.czr = end_op.czr
	and start_op.rn = 1
	and end_op.rn = 1, false, false, LocalTempView
	+- 'Project ['start_op.ywqqid, 'start_op.czr, 'round((('unix_timestamp('concat(cast('end_op.czrq as string),  , 'end_op.czsj), yyyyMMdd HH:mm:ss) - 'unix_timestamp('concat(cast('start_op.czrq as string),  , 'start_op.czsj), yyyyMMdd HH:mm:ss)) / 60.0), 2) AS fh_minutes#1559]
	   +- 'Join Inner, ((('start_op.ywqqid = 'end_op.ywqqid) AND ('start_op.czr = 'end_op.czr)) AND (('start_op.rn = 1) AND ('end_op.rn = 1)))
	      :- 'SubqueryAlias start_op
	      :  +- 'Project ['ywqqid, 'czrq, 'czsj, 'czr, 'row_number() windowspecdefinition('ywqqid, 'id ASC NULLS FIRST, unspecifiedframe$()) AS rn#1557]
	      :     +- 'Filter ((('czlx = 201) AND ('instr('jdmc, 非现复审) > 0)) AND ('czrq = 20250801))
	      :        +- 'UnresolvedRelation [TYWQQ_CZLS_mapping], [], false
	      +- 'SubqueryAlias end_op
	         +- 'Project ['ywqqid, 'czrq, 'czsj, 'czr, 'row_number() windowspecdefinition('ywqqid, 'id DESC NULLS LAST, unspecifiedframe$()) AS rn#1558]
	            +- 'Filter (('czlx IN (301,302) AND ('instr('jdmc, 非现复审) > 0)) AND ('czrq = 20250801))
	               +- 'UnresolvedRelation [TYWQQ_CZLS_mapping], [], false
	
	25/08/01 15:06:55 WARN SparkSession: Spark SQL dataset syntax checking query syntax checking 
	create temporary view wskh_fh_time as select
	        start_op.ywqqid,
	        start_op.czr,
	        round((unix_timestamp(concat(cast(end_op.czrq as string),
	        ' ',
	        end_op.czsj),
	        'yyyyMMdd HH:mm:ss') - unix_timestamp(concat(cast(start_op.czrq as string),
	        ' ',
	        start_op.czsj),
	        'yyyyMMdd HH:mm:ss')) / 60.0,
	        2) as fh_minutes 
	    from
	        ( select
	            ywqqid,
	            czrq,
	            czsj,
	            czr,
	            row_number() over(partition 
	        by
	            ywqqid 
	        order by
	            id) as rn 
	        from
	            TYWQQ_CZLS_mapping 
	        where
	            czlx = '201'   
	            and instr(jdmc,'非现复审') > 0 
	            and czrq = ${RQ} ) start_op 
	    join
	        (
	            select
	                ywqqid,
	                czrq,
	                czsj,
	                czr,
	                row_number() over(partition 
	            by
	                ywqqid 
	            order by
	                id desc) as rn 
	            from
	                TYWQQ_CZLS_mapping 
	            where
	                czlx in (
	                    '301', '302'
	                )   
	                and instr(jdmc,'非现复审') > 0 
	                and czrq = ${RQ} 
	        ) end_op 
	            on start_op.ywqqid = end_op.ywqqid 
	            and start_op.czr = end_op.czr 
	            and start_op.rn = 1 
	            and end_op.rn = 1
	++
	||
	++
	++
	
	25/08/01 15:06:55 INFO SparkSession: 
	create temporary view temp_wskh_ywshsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(a.result,
	        0) as DECIMAL(20,
	        4)) as result 
	    from
	        ( select
	            sum(sh_minutes) as result 
	        from
	            wskh_sh_time ) a,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YWSHSC';
	25/08/01 15:06:55 INFO SparkSession: Logical interpretation plan:
	'CreateViewStatement [temp_wskh_ywshsc], select
	20250801 as rq,
	t.id as idx_id,
	t.idx_code as idx_code,
	cast(coalesce(a.result, 0) as DECIMAL(20,4)) as result
	from
	(
	select sum(sh_minutes) as result
	from wskh_sh_time
	) a,
	TIC_ZBCS_mapping t
	where
	t.idx_code = 'WSKH_YWSHSC', false, false, LocalTempView
	+- 'Project [20250801 AS rq#1584, 't.id AS idx_id#1585, 't.idx_code AS idx_code#1586, cast('coalesce('a.result, 0) as decimal(20,4)) AS result#1587]
	   +- 'Filter ('t.idx_code = WSKH_YWSHSC)
	      +- 'Join Inner
	         :- 'SubqueryAlias a
	         :  +- 'Project ['sum('sh_minutes) AS result#1583]
	         :     +- 'UnresolvedRelation [wskh_sh_time], [], false
	         +- 'SubqueryAlias t
	            +- 'UnresolvedRelation [TIC_ZBCS_mapping], [], false
	
	25/08/01 15:06:55 WARN SparkSession: Spark SQL dataset syntax checking query syntax checking 
	create temporary view temp_wskh_ywshsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(a.result,
	        0) as DECIMAL(20,
	        4)) as result 
	    from
	        ( select
	            sum(sh_minutes) as result 
	        from
	            wskh_sh_time ) a,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YWSHSC'
	++
	||
	++
	++
	
	25/08/01 15:06:55 INFO SparkSession: 
	create temporary view temp_wskh_ywpjmbshsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(case 
	            when b.ywbs = 0 then 0 
	            else round(a.result / b.ywbs,
	            2) 
	        end,
	        0) as DECIMAL(20,
	        4)) as result 
	    from
	        ( select
	            sum(sh_minutes) as result 
	        from
	            wskh_sh_time ) a,
	        ( select
	            count(distinct ywqqid) as ywbs 
	        from
	            wskh_base ) b,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YWPJMBSHSC';
	25/08/01 15:06:56 INFO SparkSession: Logical interpretation plan:
	'CreateViewStatement [temp_wskh_ywpjmbshsc], select
	20250801 as rq,
	t.id as idx_id,
	t.idx_code as idx_code,
	cast(coalesce(case when b.ywbs = 0 then 0 else round(a.result / b.ywbs, 2) end, 0) as DECIMAL(20,4)) as result
	from
	(
	select sum(sh_minutes) as result
	from wskh_sh_time
	) a,
	(
	select count(distinct ywqqid) as ywbs
	from wskh_base
	) b,
	TIC_ZBCS_mapping t
	where
	t.idx_code = 'WSKH_YWPJMBSHSC', false, false, LocalTempView
	+- 'Project [20250801 AS rq#1622, 't.id AS idx_id#1623, 't.idx_code AS idx_code#1624, cast('coalesce(CASE WHEN ('b.ywbs = 0) THEN 0 ELSE 'round(('a.result / 'b.ywbs), 2) END, 0) as decimal(20,4)) AS result#1625]
	   +- 'Filter ('t.idx_code = WSKH_YWPJMBSHSC)
	      +- 'Join Inner
	         :- 'Join Inner
	         :  :- 'SubqueryAlias a
	         :  :  +- 'Project ['sum('sh_minutes) AS result#1620]
	         :  :     +- 'UnresolvedRelation [wskh_sh_time], [], false
	         :  +- 'SubqueryAlias b
	         :     +- 'Project ['count(distinct 'ywqqid) AS ywbs#1621]
	         :        +- 'UnresolvedRelation [wskh_base], [], false
	         +- 'SubqueryAlias t
	            +- 'UnresolvedRelation [TIC_ZBCS_mapping], [], false
	
	25/08/01 15:06:56 WARN SparkSession: Spark SQL dataset syntax checking query syntax checking 
	create temporary view temp_wskh_ywpjmbshsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(case 
	            when b.ywbs = 0 then 0 
	            else round(a.result / b.ywbs,
	            2) 
	        end,
	        0) as DECIMAL(20,
	        4)) as result 
	    from
	        ( select
	            sum(sh_minutes) as result 
	        from
	            wskh_sh_time ) a,
	        ( select
	            count(distinct ywqqid) as ywbs 
	        from
	            wskh_base ) b,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YWPJMBSHSC'
	++
	||
	++
	++
	
	25/08/01 15:06:56 INFO SparkSession: 
	create temporary view temp_wskh_ywpjmbdhsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(case 
	            when b.ywbs = 0 then 0 
	            else round(a.result / b.ywbs,
	            2) 
	        end,
	        0) as DECIMAL(20,
	        4)) as result 
	    from
	        ( select
	            sum(dh_minutes) as result 
	        from
	            wskh_dh_time ) a,
	        ( select
	            count(distinct ywqqid) as ywbs 
	        from
	            wskh_base ) b,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YWPJMBDHSC';
	25/08/01 15:06:56 INFO SparkSession: Logical interpretation plan:
	'CreateViewStatement [temp_wskh_ywpjmbdhsc], select
	20250801 as rq,
	t.id as idx_id,
	t.idx_code as idx_code,
	cast(coalesce(case when b.ywbs = 0 then 0 else round(a.result / b.ywbs, 2) end, 0) as DECIMAL(20,4)) as result
	from
	(
	select sum(dh_minutes) as result
	from wskh_dh_time
	) a,
	(
	select count(distinct ywqqid) as ywbs
	from wskh_base
	) b,
	TIC_ZBCS_mapping t
	where
	t.idx_code = 'WSKH_YWPJMBDHSC', false, false, LocalTempView
	+- 'Project [20250801 AS rq#1667, 't.id AS idx_id#1668, 't.idx_code AS idx_code#1669, cast('coalesce(CASE WHEN ('b.ywbs = 0) THEN 0 ELSE 'round(('a.result / 'b.ywbs), 2) END, 0) as decimal(20,4)) AS result#1670]
	   +- 'Filter ('t.idx_code = WSKH_YWPJMBDHSC)
	      +- 'Join Inner
	         :- 'Join Inner
	         :  :- 'SubqueryAlias a
	         :  :  +- 'Project ['sum('dh_minutes) AS result#1665]
	         :  :     +- 'UnresolvedRelation [wskh_dh_time], [], false
	         :  +- 'SubqueryAlias b
	         :     +- 'Project ['count(distinct 'ywqqid) AS ywbs#1666]
	         :        +- 'UnresolvedRelation [wskh_base], [], false
	         +- 'SubqueryAlias t
	            +- 'UnresolvedRelation [TIC_ZBCS_mapping], [], false
	
	25/08/01 15:06:56 WARN SparkSession: Spark SQL dataset syntax checking query syntax checking 
	create temporary view temp_wskh_ywpjmbdhsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(case 
	            when b.ywbs = 0 then 0 
	            else round(a.result / b.ywbs,
	            2) 
	        end,
	        0) as DECIMAL(20,
	        4)) as result 
	    from
	        ( select
	            sum(dh_minutes) as result 
	        from
	            wskh_dh_time ) a,
	        ( select
	            count(distinct ywqqid) as ywbs 
	        from
	            wskh_base ) b,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YWPJMBDHSC'
	++
	||
	++
	++
	
	25/08/01 15:06:56 INFO SparkSession: 
	create temporary view temp_wskh_ywfhsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(a.result,
	        0) as DECIMAL(20,
	        4)) as result 
	    from
	        ( select
	            sum(fh_minutes) as result 
	        from
	            wskh_fh_time ) a,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YWFHSC';
	25/08/01 15:06:56 INFO SparkSession: Logical interpretation plan:
	'CreateViewStatement [temp_wskh_ywfhsc], select
	20250801 as rq,
	t.id as idx_id,
	t.idx_code as idx_code,
	cast(coalesce(a.result, 0) as DECIMAL(20,4)) as result
	from
	(
	select sum(fh_minutes) as result
	from wskh_fh_time
	) a,
	TIC_ZBCS_mapping t
	where
	t.idx_code = 'WSKH_YWFHSC', false, false, LocalTempView
	+- 'Project [20250801 AS rq#2023, 't.id AS idx_id#2024, 't.idx_code AS idx_code#2025, cast('coalesce('a.result, 0) as decimal(20,4)) AS result#2026]
	   +- 'Filter ('t.idx_code = WSKH_YWFHSC)
	      +- 'Join Inner
	         :- 'SubqueryAlias a
	         :  +- 'Project ['sum('fh_minutes) AS result#2022]
	         :     +- 'UnresolvedRelation [wskh_fh_time], [], false
	         +- 'SubqueryAlias t
	            +- 'UnresolvedRelation [TIC_ZBCS_mapping], [], false
	
	25/08/01 15:06:56 WARN SparkSession: Spark SQL dataset syntax checking query syntax checking 
	create temporary view temp_wskh_ywfhsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(a.result,
	        0) as DECIMAL(20,
	        4)) as result 
	    from
	        ( select
	            sum(fh_minutes) as result 
	        from
	            wskh_fh_time ) a,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YWFHSC'
	++
	||
	++
	++
	
	25/08/01 15:06:56 INFO SparkSession: 
	create temporary view temp_wskh_ywpjmbfhsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(case 
	            when b.ywbs = 0 then 0 
	            else round(a.result / b.ywbs,
	            2) 
	        end,
	        0) as DECIMAL(20,
	        4)) as result 
	    from
	        ( select
	            sum(fh_minutes) as result 
	        from
	            wskh_fh_time ) a,
	        ( select
	            count(distinct ywqqid) as ywbs 
	        from
	            wskh_base ) b,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YWPJMBFHSC';
	25/08/01 15:06:56 INFO SparkSession: Logical interpretation plan:
	'CreateViewStatement [temp_wskh_ywpjmbfhsc], select
	20250801 as rq,
	t.id as idx_id,
	t.idx_code as idx_code,
	cast(coalesce(case when b.ywbs = 0 then 0 else round(a.result / b.ywbs, 2) end, 0) as DECIMAL(20,4)) as result
	from
	(
	select sum(fh_minutes) as result
	from wskh_fh_time
	) a,
	(
	select count(distinct ywqqid) as ywbs
	from wskh_base
	) b,
	TIC_ZBCS_mapping t
	where
	t.idx_code = 'WSKH_YWPJMBFHSC', false, false, LocalTempView
	+- 'Project [20250801 AS rq#2061, 't.id AS idx_id#2062, 't.idx_code AS idx_code#2063, cast('coalesce(CASE WHEN ('b.ywbs = 0) THEN 0 ELSE 'round(('a.result / 'b.ywbs), 2) END, 0) as decimal(20,4)) AS result#2064]
	   +- 'Filter ('t.idx_code = WSKH_YWPJMBFHSC)
	      +- 'Join Inner
	         :- 'Join Inner
	         :  :- 'SubqueryAlias a
	         :  :  +- 'Project ['sum('fh_minutes) AS result#2059]
	         :  :     +- 'UnresolvedRelation [wskh_fh_time], [], false
	         :  +- 'SubqueryAlias b
	         :     +- 'Project ['count(distinct 'ywqqid) AS ywbs#2060]
	         :        +- 'UnresolvedRelation [wskh_base], [], false
	         +- 'SubqueryAlias t
	            +- 'UnresolvedRelation [TIC_ZBCS_mapping], [], false
	
	25/08/01 15:06:56 WARN SparkSession: Spark SQL dataset syntax checking query syntax checking 
	create temporary view temp_wskh_ywpjmbfhsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(case 
	            when b.ywbs = 0 then 0 
	            else round(a.result / b.ywbs,
	            2) 
	        end,
	        0) as DECIMAL(20,
	        4)) as result 
	    from
	        ( select
	            sum(fh_minutes) as result 
	        from
	            wskh_fh_time ) a,
	        ( select
	            count(distinct ywqqid) as ywbs 
	        from
	            wskh_base ) b,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YWPJMBFHSC'
	++
	||
	++
	++
	
	25/08/01 15:06:56 INFO SparkSession: 
	create temporary view temp_wskh_ywpjmbblsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(case 
	            when b.ywbs = 0 then 0 
	            else round(a.result / b.ywbs,
	            2) 
	        end,
	        0) as DECIMAL(20,
	        4)) as result 
	    from
	        ( select
	            coalesce(sh.total_sh,
	            0) + coalesce(fh.total_fh,
	            0) + coalesce(dh.total_dh,
	            0) as result 
	        from
	            (select
	                sum(sh_minutes) as total_sh 
	            from
	                wskh_sh_time) sh,
	            (select
	                sum(fh_minutes) as total_fh 
	            from
	                wskh_fh_time) fh,
	            (select
	                sum(dh_minutes) as total_dh 
	            from
	                wskh_dh_time) dh ) a,
	            ( select
	                count(distinct ywqqid) as ywbs 
	            from
	                wskh_base ) b,
	            TIC_ZBCS_mapping t 
	        where
	            t.idx_code = 'WSKH_YWPJMBBLSC';
	25/08/01 15:06:56 INFO SparkSession: Logical interpretation plan:
	'CreateViewStatement [temp_wskh_ywpjmbblsc], select
	20250801 as rq,
	t.id as idx_id,
	t.idx_code as idx_code,
	cast(coalesce(case when b.ywbs = 0 then 0 else round(a.result / b.ywbs, 2) end, 0) as DECIMAL(20,4)) as result
	from
	(
	select
	coalesce(sh.total_sh, 0) + coalesce(fh.total_fh, 0) + coalesce(dh.total_dh, 0) as result
	from
	(select sum(sh_minutes) as total_sh from wskh_sh_time) sh,
	(select sum(fh_minutes) as total_fh from wskh_fh_time) fh,
	(select sum(dh_minutes) as total_dh from wskh_dh_time) dh
	) a,
	(
	select count(distinct ywqqid) as ywbs
	from wskh_base
	) b,
	TIC_ZBCS_mapping t
	where
	t.idx_code = 'WSKH_YWPJMBBLSC', false, false, LocalTempView
	+- 'Project [20250801 AS rq#2109, 't.id AS idx_id#2110, 't.idx_code AS idx_code#2111, cast('coalesce(CASE WHEN ('b.ywbs = 0) THEN 0 ELSE 'round(('a.result / 'b.ywbs), 2) END, 0) as decimal(20,4)) AS result#2112]
	   +- 'Filter ('t.idx_code = WSKH_YWPJMBBLSC)
	      +- 'Join Inner
	         :- 'Join Inner
	         :  :- 'SubqueryAlias a
	         :  :  +- 'Project [(('coalesce('sh.total_sh, 0) + 'coalesce('fh.total_fh, 0)) + 'coalesce('dh.total_dh, 0)) AS result#2107]
	         :  :     +- 'Join Inner
	         :  :        :- 'Join Inner
	         :  :        :  :- 'SubqueryAlias sh
	         :  :        :  :  +- 'Project ['sum('sh_minutes) AS total_sh#2104]
	         :  :        :  :     +- 'UnresolvedRelation [wskh_sh_time], [], false
	         :  :        :  +- 'SubqueryAlias fh
	         :  :        :     +- 'Project ['sum('fh_minutes) AS total_fh#2105]
	         :  :        :        +- 'UnresolvedRelation [wskh_fh_time], [], false
	         :  :        +- 'SubqueryAlias dh
	         :  :           +- 'Project ['sum('dh_minutes) AS total_dh#2106]
	         :  :              +- 'UnresolvedRelation [wskh_dh_time], [], false
	         :  +- 'SubqueryAlias b
	         :     +- 'Project ['count(distinct 'ywqqid) AS ywbs#2108]
	         :        +- 'UnresolvedRelation [wskh_base], [], false
	         +- 'SubqueryAlias t
	            +- 'UnresolvedRelation [TIC_ZBCS_mapping], [], false
	
[INFO] 2025-08-01 15:06:57.715 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[69] -  -> ++
	||
	++
	++
	
	25/08/01 15:06:57 INFO SparkSession: 
	create temporary view temp_wskh_ygywshsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(a.result,
	        0) as DECIMAL(20,
	        4)) as result,
	        a.czr as yg 
	    from
	        ( select
	            czr,
	            sum(sh_minutes) as result 
	        from
	            wskh_sh_time 
	        group by
	            czr ) a,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YGYWSHSC';
	25/08/01 15:06:57 INFO SparkSession: Logical interpretation plan:
	'CreateViewStatement [temp_wskh_ygywshsc], select
	20250801 as rq,
	t.id as idx_id,
	t.idx_code as idx_code,
	cast(coalesce(a.result, 0) as DECIMAL(20,4)) as result,
	a.czr as yg
	from
	(
	select czr, sum(sh_minutes) as result
	from wskh_sh_time
	group by czr
	) a,
	TIC_ZBCS_mapping t
	where
	t.idx_code = 'WSKH_YGYWSHSC', false, false, LocalTempView
	+- 'Project [20250801 AS rq#2554, 't.id AS idx_id#2555, 't.idx_code AS idx_code#2556, cast('coalesce('a.result, 0) as decimal(20,4)) AS result#2557, 'a.czr AS yg#2558]
	   +- 'Filter ('t.idx_code = WSKH_YGYWSHSC)
	      +- 'Join Inner
	         :- 'SubqueryAlias a
	         :  +- 'Aggregate ['czr], ['czr, 'sum('sh_minutes) AS result#2553]
	         :     +- 'UnresolvedRelation [wskh_sh_time], [], false
	         +- 'SubqueryAlias t
	            +- 'UnresolvedRelation [TIC_ZBCS_mapping], [], false
	
	25/08/01 15:06:57 WARN SparkSession: Spark SQL dataset syntax checking query syntax checking 
	create temporary view temp_wskh_ygywshsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(a.result,
	        0) as DECIMAL(20,
	        4)) as result,
	        a.czr as yg 
	    from
	        ( select
	            czr,
	            sum(sh_minutes) as result 
	        from
	            wskh_sh_time 
	        group by
	            czr ) a,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YGYWSHSC'
	++
	||
	++
	++
	
	25/08/01 15:06:57 INFO SparkSession: 
	create temporary view temp_wskh_ygywpjmbshsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(case 
	            when b.ywbs = 0 then 0 
	            else round(a.result / b.ywbs,
	            2) 
	        end,
	        0) as DECIMAL(20,
	        4)) as result,
	        a.czr as yg 
	    from
	        ( select
	            czr,
	            sum(sh_minutes) as result 
	        from
	            wskh_sh_time 
	        group by
	            czr ) a 
	    join
	        (
	            select
	                czr,
	                count(1) as ywbs 
	            from
	                wskh_sh_time 
	            group by
	                czr 
	        ) b 
	            on a.czr = b.czr,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YGYWPJMBSHSC';
	25/08/01 15:06:57 INFO SparkSession: Logical interpretation plan:
	'CreateViewStatement [temp_wskh_ygywpjmbshsc], select
	20250801 as rq,
	t.id as idx_id,
	t.idx_code as idx_code,
	cast(coalesce(case when b.ywbs = 0 then 0 else round(a.result / b.ywbs, 2) end, 0) as DECIMAL(20,4)) as result,
	a.czr as yg
	from
	(
	select czr, sum(sh_minutes) as result
	from wskh_sh_time
	group by czr
	) a
	join (
	select czr, count(1) as ywbs
	from wskh_sh_time
	group by czr
	) b on a.czr = b.czr,
	TIC_ZBCS_mapping t
	where
	t.idx_code = 'WSKH_YGYWPJMBSHSC', false, false, LocalTempView
	+- 'Project [20250801 AS rq#2594, 't.id AS idx_id#2595, 't.idx_code AS idx_code#2596, cast('coalesce(CASE WHEN ('b.ywbs = 0) THEN 0 ELSE 'round(('a.result / 'b.ywbs), 2) END, 0) as decimal(20,4)) AS result#2597, 'a.czr AS yg#2598]
	   +- 'Filter ('t.idx_code = WSKH_YGYWPJMBSHSC)
	      +- 'Join Inner
	         :- 'Join Inner, ('a.czr = 'b.czr)
	         :  :- 'SubqueryAlias a
	         :  :  +- 'Aggregate ['czr], ['czr, 'sum('sh_minutes) AS result#2592]
	         :  :     +- 'UnresolvedRelation [wskh_sh_time], [], false
	         :  +- 'SubqueryAlias b
	         :     +- 'Aggregate ['czr], ['czr, 'count(1) AS ywbs#2593]
	         :        +- 'UnresolvedRelation [wskh_sh_time], [], false
	         +- 'SubqueryAlias t
	            +- 'UnresolvedRelation [TIC_ZBCS_mapping], [], false
	
	25/08/01 15:06:57 WARN SparkSession: Spark SQL dataset syntax checking query syntax checking 
	create temporary view temp_wskh_ygywpjmbshsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(case 
	            when b.ywbs = 0 then 0 
	            else round(a.result / b.ywbs,
	            2) 
	        end,
	        0) as DECIMAL(20,
	        4)) as result,
	        a.czr as yg 
	    from
	        ( select
	            czr,
	            sum(sh_minutes) as result 
	        from
	            wskh_sh_time 
	        group by
	            czr ) a 
	    join
	        (
	            select
	                czr,
	                count(1) as ywbs 
	            from
	                wskh_sh_time 
	            group by
	                czr 
	        ) b 
	            on a.czr = b.czr,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YGYWPJMBSHSC'
	++
	||
	++
	++
	
	25/08/01 15:06:57 INFO SparkSession: 
	create temporary view temp_wskh_ygywpjmbdhsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(case 
	            when b.ywbs = 0 then 0 
	            else round(a.result / b.ywbs,
	            2) 
	        end,
	        0) as DECIMAL(20,
	        4)) as result,
	        a.czr as yg 
	    from
	        ( select
	            czr,
	            sum(dh_minutes) as result 
	        from
	            wskh_dh_time 
	        group by
	            czr ) a 
	    join
	        (
	            select
	                czr,
	                count(1) as ywbs 
	            from
	                wskh_dh_time 
	            group by
	                czr 
	        ) b 
	            on a.czr = b.czr,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YGYWPJMBDHSC';
	25/08/01 15:06:57 INFO SparkSession: Logical interpretation plan:
	'CreateViewStatement [temp_wskh_ygywpjmbdhsc], select
	20250801 as rq,
	t.id as idx_id,
	t.idx_code as idx_code,
	cast(coalesce(case when b.ywbs = 0 then 0 else round(a.result / b.ywbs, 2) end, 0) as DECIMAL(20,4)) as result,
	a.czr as yg
	from
	(
	select czr, sum(dh_minutes) as result
	from wskh_dh_time
	group by czr
	) a
	join (
	select czr, count(1) as ywbs
	from wskh_dh_time
	group by czr
	) b on a.czr = b.czr,
	TIC_ZBCS_mapping t
	where
	t.idx_code = 'WSKH_YGYWPJMBDHSC', false, false, LocalTempView
	+- 'Project [20250801 AS rq#2678, 't.id AS idx_id#2679, 't.idx_code AS idx_code#2680, cast('coalesce(CASE WHEN ('b.ywbs = 0) THEN 0 ELSE 'round(('a.result / 'b.ywbs), 2) END, 0) as decimal(20,4)) AS result#2681, 'a.czr AS yg#2682]
	   +- 'Filter ('t.idx_code = WSKH_YGYWPJMBDHSC)
	      +- 'Join Inner
	         :- 'Join Inner, ('a.czr = 'b.czr)
	         :  :- 'SubqueryAlias a
	         :  :  +- 'Aggregate ['czr], ['czr, 'sum('dh_minutes) AS result#2676]
	         :  :     +- 'UnresolvedRelation [wskh_dh_time], [], false
	         :  +- 'SubqueryAlias b
	         :     +- 'Aggregate ['czr], ['czr, 'count(1) AS ywbs#2677]
	         :        +- 'UnresolvedRelation [wskh_dh_time], [], false
	         +- 'SubqueryAlias t
	            +- 'UnresolvedRelation [TIC_ZBCS_mapping], [], false
	
	25/08/01 15:06:57 WARN SparkSession: Spark SQL dataset syntax checking query syntax checking 
	create temporary view temp_wskh_ygywpjmbdhsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(case 
	            when b.ywbs = 0 then 0 
	            else round(a.result / b.ywbs,
	            2) 
	        end,
	        0) as DECIMAL(20,
	        4)) as result,
	        a.czr as yg 
	    from
	        ( select
	            czr,
	            sum(dh_minutes) as result 
	        from
	            wskh_dh_time 
	        group by
	            czr ) a 
	    join
	        (
	            select
	                czr,
	                count(1) as ywbs 
	            from
	                wskh_dh_time 
	            group by
	                czr 
	        ) b 
	            on a.czr = b.czr,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YGYWPJMBDHSC'
	++
	||
	++
	++
	
	25/08/01 15:06:57 INFO SparkSession: 
	create temporary view temp_wskh_ygywfhsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(a.result,
	        0) as DECIMAL(20,
	        4)) as result,
	        a.czr as yg 
	    from
	        ( select
	            czr,
	            sum(fh_minutes) as result 
	        from
	            wskh_fh_time 
	        group by
	            czr ) a,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YGYWFHSC';
	25/08/01 15:06:57 INFO SparkSession: Logical interpretation plan:
	'CreateViewStatement [temp_wskh_ygywfhsc], select
	20250801 as rq,
	t.id as idx_id,
	t.idx_code as idx_code,
	cast(coalesce(a.result, 0) as DECIMAL(20,4)) as result,
	a.czr as yg
	from
	(
	select czr, sum(fh_minutes) as result
	from wskh_fh_time
	group by czr
	) a,
	TIC_ZBCS_mapping t
	where
	t.idx_code = 'WSKH_YGYWFHSC', false, false, LocalTempView
	+- 'Project [20250801 AS rq#3059, 't.id AS idx_id#3060, 't.idx_code AS idx_code#3061, cast('coalesce('a.result, 0) as decimal(20,4)) AS result#3062, 'a.czr AS yg#3063]
	   +- 'Filter ('t.idx_code = WSKH_YGYWFHSC)
	      +- 'Join Inner
	         :- 'SubqueryAlias a
	         :  +- 'Aggregate ['czr], ['czr, 'sum('fh_minutes) AS result#3058]
	         :     +- 'UnresolvedRelation [wskh_fh_time], [], false
	         +- 'SubqueryAlias t
	            +- 'UnresolvedRelation [TIC_ZBCS_mapping], [], false
	
	25/08/01 15:06:57 WARN SparkSession: Spark SQL dataset syntax checking query syntax checking 
	create temporary view temp_wskh_ygywfhsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(a.result,
	        0) as DECIMAL(20,
	        4)) as result,
	        a.czr as yg 
	    from
	        ( select
	            czr,
	            sum(fh_minutes) as result 
	        from
	            wskh_fh_time 
	        group by
	            czr ) a,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YGYWFHSC'
[INFO] 2025-08-01 15:06:58.624 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[193] - process has exited, execute path:/home/<USER>/dolphinscheduler/exec/process/13589517760800/18507520943136_16/43179/44042, processId:20822 ,exitStatusCode:255 ,processWaitForStatus:true ,processExitValue:255
[INFO] 2025-08-01 15:06:58.718 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[69] -  -> ++
	||
	++
	++
	
	25/08/01 15:06:57 INFO SparkSession: 
	create temporary view temp_wskh_ygywpjmbfhsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(case 
	            when b.ywbs = 0 then 0 
	            else round(a.result / b.ywbs,
	            2) 
	        end,
	        0) as DECIMAL(20,
	        4)) as result,
	        a.czr as yg 
	    from
	        ( select
	            czr,
	            sum(fh_minutes) as result 
	        from
	            wskh_fh_time 
	        group by
	            czr ) a 
	    join
	        (
	            select
	                czr,
	                count(1) as ywbs 
	            from
	                wskh_fh_time 
	            group by
	                czr 
	        ) b 
	            on a.czr = b.czr,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YGYWPJMBFHSC';
	25/08/01 15:06:57 INFO SparkSession: Logical interpretation plan:
	'CreateViewStatement [temp_wskh_ygywpjmbfhsc], select
	20250801 as rq,
	t.id as idx_id,
	t.idx_code as idx_code,
	cast(coalesce(case when b.ywbs = 0 then 0 else round(a.result / b.ywbs, 2) end, 0) as DECIMAL(20,4)) as result,
	a.czr as yg
	from
	(
	select czr, sum(fh_minutes) as result
	from wskh_fh_time
	group by czr
	) a
	join (
	select czr, count(1) as ywbs
	from wskh_fh_time
	group by czr
	) b on a.czr = b.czr,
	TIC_ZBCS_mapping t
	where
	t.idx_code = 'WSKH_YGYWPJMBFHSC', false, false, LocalTempView
	+- 'Project [20250801 AS rq#3099, 't.id AS idx_id#3100, 't.idx_code AS idx_code#3101, cast('coalesce(CASE WHEN ('b.ywbs = 0) THEN 0 ELSE 'round(('a.result / 'b.ywbs), 2) END, 0) as decimal(20,4)) AS result#3102, 'a.czr AS yg#3103]
	   +- 'Filter ('t.idx_code = WSKH_YGYWPJMBFHSC)
	      +- 'Join Inner
	         :- 'Join Inner, ('a.czr = 'b.czr)
	         :  :- 'SubqueryAlias a
	         :  :  +- 'Aggregate ['czr], ['czr, 'sum('fh_minutes) AS result#3097]
	         :  :     +- 'UnresolvedRelation [wskh_fh_time], [], false
	         :  +- 'SubqueryAlias b
	         :     +- 'Aggregate ['czr], ['czr, 'count(1) AS ywbs#3098]
	         :        +- 'UnresolvedRelation [wskh_fh_time], [], false
	         +- 'SubqueryAlias t
	            +- 'UnresolvedRelation [TIC_ZBCS_mapping], [], false
	
	25/08/01 15:06:57 WARN SparkSession: Spark SQL dataset syntax checking query syntax checking 
	create temporary view temp_wskh_ygywpjmbfhsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(case 
	            when b.ywbs = 0 then 0 
	            else round(a.result / b.ywbs,
	            2) 
	        end,
	        0) as DECIMAL(20,
	        4)) as result,
	        a.czr as yg 
	    from
	        ( select
	            czr,
	            sum(fh_minutes) as result 
	        from
	            wskh_fh_time 
	        group by
	            czr ) a 
	    join
	        (
	            select
	                czr,
	                count(1) as ywbs 
	            from
	                wskh_fh_time 
	            group by
	                czr 
	        ) b 
	            on a.czr = b.czr,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YGYWPJMBFHSC'
	++
	||
	++
	++
	
	25/08/01 15:06:57 INFO SparkSession: 
	create temporary view temp_wskh_ygywpjmbblsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(case 
	            when b.ywbs = 0 then 0 
	            else round(a.result / b.ywbs,
	            2) 
	        end,
	        0) as DECIMAL(20,
	        4)) as result,
	        a.czr as yg 
	    from
	        ( select
	            czr,
	            coalesce(sh_total,
	            0) + coalesce(fh_total,
	            0) + coalesce(dh_total,
	            0) as result 
	        from
	            ( select
	                czr,
	                sum(sh_minutes) as sh_total 
	            from
	                wskh_sh_time 
	            group by
	                czr ) sh full 
	        outer join
	            (
	                select
	                    czr,
	                    sum(fh_minutes) as fh_total 
	                from
	                    wskh_fh_time 
	                group by
	                    czr 
	            ) fh 
	                on sh.czr = fh.czr full 
	        outer join
	            (
	                select
	                    czr,
	                    sum(dh_minutes) as dh_total 
	                from
	                    wskh_dh_time 
	                group by
	                    czr 
	            ) dh 
	                on coalesce(sh.czr,
	            fh.czr) = dh.czr ) a 
	        join
	            (
	                select
	                    czr,
	                    count(distinct ywqqid) as ywbs 
	                from
	                    ( select
	                        czr,
	                        ywqqid 
	                    from
	                        wskh_sh_time 
	                    union
	                    select
	                        czr,
	                        ywqqid 
	                    from
	                        wskh_fh_time 
	                    union
	                    select
	                        czr,
	                        ywqqid 
	                    from
	                        wskh_dh_time 
	                ) combined 
	            group by
	                czr 
	        ) b 
	            on a.czr = b.czr,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YGYWPJMBBLSC';
	25/08/01 15:06:57 INFO SparkSession: Logical interpretation plan:
	'CreateViewStatement [temp_wskh_ygywpjmbblsc], select
	20250801 as rq,
	t.id as idx_id,
	t.idx_code as idx_code,
	cast(coalesce(case when b.ywbs = 0 then 0 else round(a.result / b.ywbs, 2) end, 0) as DECIMAL(20,4)) as result,
	a.czr as yg
	from
	(
	select
	czr,
	coalesce(sh_total, 0) + coalesce(fh_total, 0) + coalesce(dh_total, 0) as result
	from (
	select czr, sum(sh_minutes) as sh_total from wskh_sh_time group by czr
	) sh
	full outer join (
	select czr, sum(fh_minutes) as fh_total from wskh_fh_time group by czr
	) fh on sh.czr = fh.czr
	full outer join (
	select czr, sum(dh_minutes) as dh_total from wskh_dh_time group by czr
	) dh on coalesce(sh.czr, fh.czr) = dh.czr
	) a
	join (
	select
	czr,
	count(distinct ywqqid) as ywbs
	from (
	select czr, ywqqid from wskh_sh_time
	union
	select czr, ywqqid from wskh_fh_time
	union
	select czr, ywqqid from wskh_dh_time
	) combined
	group by czr
	) b on a.czr = b.czr,
	TIC_ZBCS_mapping t
	where
	t.idx_code = 'WSKH_YGYWPJMBBLSC', false, false, LocalTempView
	+- 'Project [20250801 AS rq#3186, 't.id AS idx_id#3187, 't.idx_code AS idx_code#3188, cast('coalesce(CASE WHEN ('b.ywbs = 0) THEN 0 ELSE 'round(('a.result / 'b.ywbs), 2) END, 0) as decimal(20,4)) AS result#3189, 'a.czr AS yg#3190]
	   +- 'Filter ('t.idx_code = WSKH_YGYWPJMBBLSC)
	      +- 'Join Inner
	         :- 'Join Inner, ('a.czr = 'b.czr)
	         :  :- 'SubqueryAlias a
	         :  :  +- 'Project ['czr, (('coalesce('sh_total, 0) + 'coalesce('fh_total, 0)) + 'coalesce('dh_total, 0)) AS result#3184]
	         :  :     +- 'Join FullOuter, ('coalesce('sh.czr, 'fh.czr) = 'dh.czr)
	         :  :        :- 'Join FullOuter, ('sh.czr = 'fh.czr)
	         :  :        :  :- 'SubqueryAlias sh
	         :  :        :  :  +- 'Aggregate ['czr], ['czr, 'sum('sh_minutes) AS sh_total#3181]
	         :  :        :  :     +- 'UnresolvedRelation [wskh_sh_time], [], false
	         :  :        :  +- 'SubqueryAlias fh
	         :  :        :     +- 'Aggregate ['czr], ['czr, 'sum('fh_minutes) AS fh_total#3182]
	         :  :        :        +- 'UnresolvedRelation [wskh_fh_time], [], false
	         :  :        +- 'SubqueryAlias dh
	         :  :           +- 'Aggregate ['czr], ['czr, 'sum('dh_minutes) AS dh_total#3183]
	         :  :              +- 'UnresolvedRelation [wskh_dh_time], [], false
	         :  +- 'SubqueryAlias b
	         :     +- 'Aggregate ['czr], ['czr, 'count(distinct 'ywqqid) AS ywbs#3185]
	         :        +- 'SubqueryAlias combined
	         :           +- 'Distinct
	         :              +- 'Union false, false
	         :                 :- 'Distinct
	         :                 :  +- 'Union false, false
	         :                 :     :- 'Project ['czr, 'ywqqid]
	         :                 :     :  +- 'UnresolvedRelation [wskh_sh_time], [], false
	         :                 :     +- 'Project ['czr, 'ywqqid]
	         :                 :        +- 'UnresolvedRelation [wskh_fh_time], [], false
	         :                 +- 'Project ['czr, 'ywqqid]
	         :                    +- 'UnresolvedRelation [wskh_dh_time], [], false
	         +- 'SubqueryAlias t
	            +- 'UnresolvedRelation [TIC_ZBCS_mapping], [], false
	
	25/08/01 15:06:57 WARN SparkSession: Spark SQL dataset syntax checking query syntax checking 
	create temporary view temp_wskh_ygywpjmbblsc as select
	        ${RQ} as rq,
	        t.id as idx_id,
	        t.idx_code as idx_code,
	        cast(coalesce(case 
	            when b.ywbs = 0 then 0 
	            else round(a.result / b.ywbs,
	            2) 
	        end,
	        0) as DECIMAL(20,
	        4)) as result,
	        a.czr as yg 
	    from
	        ( select
	            czr,
	            coalesce(sh_total,
	            0) + coalesce(fh_total,
	            0) + coalesce(dh_total,
	            0) as result 
	        from
	            ( select
	                czr,
	                sum(sh_minutes) as sh_total 
	            from
	                wskh_sh_time 
	            group by
	                czr ) sh full 
	        outer join
	            (
	                select
	                    czr,
	                    sum(fh_minutes) as fh_total 
	                from
	                    wskh_fh_time 
	                group by
	                    czr 
	            ) fh 
	                on sh.czr = fh.czr full 
	        outer join
	            (
	                select
	                    czr,
	                    sum(dh_minutes) as dh_total 
	                from
	                    wskh_dh_time 
	                group by
	                    czr 
	            ) dh 
	                on coalesce(sh.czr,
	            fh.czr) = dh.czr ) a 
	        join
	            (
	                select
	                    czr,
	                    count(distinct ywqqid) as ywbs 
	                from
	                    ( select
	                        czr,
	                        ywqqid 
	                    from
	                        wskh_sh_time 
	                    union
	                    select
	                        czr,
	                        ywqqid 
	                    from
	                        wskh_fh_time 
	                    union
	                    select
	                        czr,
	                        ywqqid 
	                    from
	                        wskh_dh_time 
	                ) combined 
	            group by
	                czr 
	        ) b 
	            on a.czr = b.czr,
	        TIC_ZBCS_mapping t 
	    where
	        t.idx_code = 'WSKH_YGYWPJMBBLSC'
	25/08/01 15:06:58 ERROR SparkBatchExecution: Error status: 
	java.lang.RuntimeException: SQL parse failed:
	create temporary view temp_wskh_ygywpjmbblsc as
	select
	${RQ} as rq,
	t.id as idx_id,
	t.idx_code as idx_code,
	cast(coalesce(case when b.ywbs = 0 then 0 else round(a.result / b.ywbs, 2) end, 0) as DECIMAL(20,4)) as result,
	a.czr as yg
	from
	(
	select
	czr,
	coalesce(sh_total, 0) + coalesce(fh_total, 0) + coalesce(dh_total, 0) as result
	from (
	select czr, sum(sh_minutes) as sh_total from wskh_sh_time group by czr
	) sh
	full outer join (
	select czr, sum(fh_minutes) as fh_total from wskh_fh_time group by czr
	) fh on sh.czr = fh.czr
	full outer join (
	select czr, sum(dh_minutes) as dh_total from wskh_dh_time group by czr
	) dh on coalesce(sh.czr, fh.czr) = dh.czr
	) a
	join (
	select
	czr,
	count(distinct ywqqid) as ywbs
	from (
	select czr, ywqqid from wskh_sh_time
	union
	select czr, ywqqid from wskh_fh_time
	union
	select czr, ywqqid from wskh_dh_time
	) combined
	group by czr
	) b on a.czr = b.czr,
	TIC_ZBCS_mapping t
	where
	t.idx_code = 'WSKH_YGYWPJMBBLSC'
	
		at com.apex.spark.transform.Sql.callCreateTable(Sql.java:585)
		at com.apex.spark.transform.Sql.callCommand(Sql.java:321)
		at com.apex.spark.transform.Sql.process(Sql.java:184)
		at com.apex.spark.batch.SparkBatchExecution.start(SparkBatchExecution.java:99)
		at com.apex.spark.job.RunJob.run(RunJob.java:175)
		at com.apex.spark.client.RunSparkEngine.main(RunSparkEngine.java:43)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.spark.deploy.JavaMainApplication.start(SparkApplication.scala:52)
		at org.apache.spark.deploy.SparkSubmit.org$apache$spark$deploy$SparkSubmit$$runMain(SparkSubmit.scala:966)
		at org.apache.spark.deploy.SparkSubmit.doRunMain$1(SparkSubmit.scala:191)
		at org.apache.spark.deploy.SparkSubmit.submit(SparkSubmit.scala:214)
		at org.apache.spark.deploy.SparkSubmit.doSubmit(SparkSubmit.scala:90)
		at org.apache.spark.deploy.SparkSubmit$$anon$2.doSubmit(SparkSubmit.scala:1054)
		at org.apache.spark.deploy.SparkSubmit$.main(SparkSubmit.scala:1063)
		at org.apache.spark.deploy.SparkSubmit.main(SparkSubmit.scala)
	Caused by: org.apache.spark.sql.AnalysisException: Reference 'czr' is ambiguous, could be: sh.czr, fh.czr, dh.czr.; line 11 pos 0
		at org.apache.spark.sql.catalyst.expressions.package$AttributeSeq.resolve(package.scala:372)
		at org.apache.spark.sql.catalyst.plans.logical.LogicalPlan.resolveChildren(LogicalPlan.scala:112)
		at org.apache.spark.sql.catalyst.analysis.Analyzer.$anonfun$resolveExpressionByPlanChildren$1(Analyzer.scala:1880)
		at org.apache.spark.sql.catalyst.analysis.Analyzer.$anonfun$resolveExpression$2(Analyzer.scala:1810)
		at org.apache.spark.sql.catalyst.analysis.package$.withPosition(package.scala:60)
		at org.apache.spark.sql.catalyst.analysis.Analyzer.innerResolve$1(Analyzer.scala:1817)
		at org.apache.spark.sql.catalyst.analysis.Analyzer.resolveExpression(Analyzer.scala:1835)
		at org.apache.spark.sql.catalyst.analysis.Analyzer.resolveExpressionByPlanChildren(Analyzer.scala:1886)
		at org.apache.spark.sql.catalyst.analysis.Analyzer$ResolveReferences$$anonfun$apply$17.$anonfun$applyOrElse$97(Analyzer.scala:1600)
		at org.apache.spark.sql.catalyst.plans.QueryPlan.$anonfun$mapExpressions$1(QueryPlan.scala:193)
		at org.apache.spark.sql.catalyst.trees.CurrentOrigin$.withOrigin(TreeNode.scala:82)
		at org.apache.spark.sql.catalyst.plans.QueryPlan.transformExpression$1(QueryPlan.scala:193)
		at org.apache.spark.sql.catalyst.plans.QueryPlan.recursiveTransform$1(QueryPlan.scala:204)
		at org.apache.spark.sql.catalyst.plans.QueryPlan.$anonfun$mapExpressions$3(QueryPlan.scala:209)
		at scala.collection.TraversableLike.$anonfun$map$1(TraversableLike.scala:286)
		at scala.collection.immutable.List.foreach(List.scala:431)
		at scala.collection.TraversableLike.map(TraversableLike.scala:286)
		at scala.collection.TraversableLike.map$(TraversableLike.scala:279)
		at scala.collection.immutable.List.map(List.scala:305)
		at org.apache.spark.sql.catalyst.plans.QueryPlan.recursiveTransform$1(QueryPlan.scala:209)
		at org.apache.spark.sql.catalyst.plans.QueryPlan.$anonfun$mapExpressions$4(QueryPlan.scala:214)
		at org.apache.spark.sql.catalyst.trees.TreeNode.mapProductIterator(TreeNode.scala:323)
		at org.apache.spark.sql.catalyst.plans.QueryPlan.mapExpressions(QueryPlan.scala:214)
		at org.apache.spark.sql.catalyst.analysis.Analyzer$ResolveReferences$$anonfun$apply$17.applyOrElse(Analyzer.scala:1600)
		at org.apache.spark.sql.catalyst.analysis.Analyzer$ResolveReferences$$anonfun$apply$17.applyOrElse(Analyzer.scala:1438)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.$anonfun$resolveOperatorsUpWithPruning$3(AnalysisHelper.scala:138)
		at org.apache.spark.sql.catalyst.trees.CurrentOrigin$.withOrigin(TreeNode.scala:82)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.$anonfun$resolveOperatorsUpWithPruning$1(AnalysisHelper.scala:138)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper$.allowInvokingTransformsInAnalyzer(AnalysisHelper.scala:323)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.resolveOperatorsUpWithPruning(AnalysisHelper.scala:134)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.resolveOperatorsUpWithPruning$(AnalysisHelper.scala:130)
		at org.apache.spark.sql.catalyst.plans.logical.LogicalPlan.resolveOperatorsUpWithPruning(LogicalPlan.scala:30)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.$anonfun$resolveOperatorsUpWithPruning$2(AnalysisHelper.scala:135)
		at org.apache.spark.sql.catalyst.trees.UnaryLike.mapChildren(TreeNode.scala:1122)
		at org.apache.spark.sql.catalyst.trees.UnaryLike.mapChildren$(TreeNode.scala:1121)
		at org.apache.spark.sql.catalyst.plans.logical.OrderPreservingUnaryNode.mapChildren(LogicalPlan.scala:206)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.$anonfun$resolveOperatorsUpWithPruning$1(AnalysisHelper.scala:135)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper$.allowInvokingTransformsInAnalyzer(AnalysisHelper.scala:323)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.resolveOperatorsUpWithPruning(AnalysisHelper.scala:134)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.resolveOperatorsUpWithPruning$(AnalysisHelper.scala:130)
		at org.apache.spark.sql.catalyst.plans.logical.LogicalPlan.resolveOperatorsUpWithPruning(LogicalPlan.scala:30)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.$anonfun$resolveOperatorsUpWithPruning$2(AnalysisHelper.scala:135)
		at org.apache.spark.sql.catalyst.trees.BinaryLike.mapChildren(TreeNode.scala:1148)
		at org.apache.spark.sql.catalyst.trees.BinaryLike.mapChildren$(TreeNode.scala:1147)
		at org.apache.spark.sql.catalyst.plans.logical.Join.mapChildren(basicLogicalOperators.scala:390)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.$anonfun$resolveOperatorsUpWithPruning$1(AnalysisHelper.scala:135)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper$.allowInvokingTransformsInAnalyzer(AnalysisHelper.scala:323)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.resolveOperatorsUpWithPruning(AnalysisHelper.scala:134)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.resolveOperatorsUpWithPruning$(AnalysisHelper.scala:130)
		at org.apache.spark.sql.catalyst.plans.logical.LogicalPlan.resolveOperatorsUpWithPruning(LogicalPlan.scala:30)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.$anonfun$resolveOperatorsUpWithPruning$2(AnalysisHelper.scala:135)
		at org.apache.spark.sql.catalyst.trees.BinaryLike.mapChildren(TreeNode.scala:1148)
		at org.apache.spark.sql.catalyst.trees.BinaryLike.mapChildren$(TreeNode.scala:1147)
		at org.apache.spark.sql.catalyst.plans.logical.Join.mapChildren(basicLogicalOperators.scala:390)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.$anonfun$resolveOperatorsUpWithPruning$1(AnalysisHelper.scala:135)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper$.allowInvokingTransformsInAnalyzer(AnalysisHelper.scala:323)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.resolveOperatorsUpWithPruning(AnalysisHelper.scala:134)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.resolveOperatorsUpWithPruning$(AnalysisHelper.scala:130)
		at org.apache.spark.sql.catalyst.plans.logical.LogicalPlan.resolveOperatorsUpWithPruning(LogicalPlan.scala:30)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.$anonfun$resolveOperatorsUpWithPruning$2(AnalysisHelper.scala:135)
		at org.apache.spark.sql.catalyst.trees.UnaryLike.mapChildren(TreeNode.scala:1122)
		at org.apache.spark.sql.catalyst.trees.UnaryLike.mapChildren$(TreeNode.scala:1121)
		at org.apache.spark.sql.catalyst.plans.logical.OrderPreservingUnaryNode.mapChildren(LogicalPlan.scala:206)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.$anonfun$resolveOperatorsUpWithPruning$1(AnalysisHelper.scala:135)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper$.allowInvokingTransformsInAnalyzer(AnalysisHelper.scala:323)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.resolveOperatorsUpWithPruning(AnalysisHelper.scala:134)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.resolveOperatorsUpWithPruning$(AnalysisHelper.scala:130)
		at org.apache.spark.sql.catalyst.plans.logical.LogicalPlan.resolveOperatorsUpWithPruning(LogicalPlan.scala:30)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.$anonfun$resolveOperatorsUpWithPruning$2(AnalysisHelper.scala:135)
		at org.apache.spark.sql.catalyst.trees.UnaryLike.mapChildren(TreeNode.scala:1122)
		at org.apache.spark.sql.catalyst.trees.UnaryLike.mapChildren$(TreeNode.scala:1121)
		at org.apache.spark.sql.catalyst.plans.logical.OrderPreservingUnaryNode.mapChildren(LogicalPlan.scala:206)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.$anonfun$resolveOperatorsUpWithPruning$1(AnalysisHelper.scala:135)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper$.allowInvokingTransformsInAnalyzer(AnalysisHelper.scala:323)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.resolveOperatorsUpWithPruning(AnalysisHelper.scala:134)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.resolveOperatorsUpWithPruning$(AnalysisHelper.scala:130)
		at org.apache.spark.sql.catalyst.plans.logical.LogicalPlan.resolveOperatorsUpWithPruning(LogicalPlan.scala:30)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.$anonfun$resolveOperatorsUpWithPruning$2(AnalysisHelper.scala:135)
		at scala.collection.immutable.List.map(List.scala:293)
		at org.apache.spark.sql.catalyst.trees.TreeNode.mapChildren(TreeNode.scala:595)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.$anonfun$resolveOperatorsUpWithPruning$1(AnalysisHelper.scala:135)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper$.allowInvokingTransformsInAnalyzer(AnalysisHelper.scala:323)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.resolveOperatorsUpWithPruning(AnalysisHelper.scala:134)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.resolveOperatorsUpWithPruning$(AnalysisHelper.scala:130)
		at org.apache.spark.sql.catalyst.plans.logical.LogicalPlan.resolveOperatorsUpWithPruning(LogicalPlan.scala:30)
		at org.apache.spark.sql.catalyst.analysis.Analyzer$ResolveReferences$.apply(Analyzer.scala:1438)
		at org.apache.spark.sql.catalyst.analysis.Analyzer$ResolveReferences$.apply(Analyzer.scala:1418)
		at org.apache.spark.sql.catalyst.rules.RuleExecutor.$anonfun$execute$2(RuleExecutor.scala:211)
		at scala.collection.LinearSeqOptimized.foldLeft(LinearSeqOptimized.scala:126)
		at scala.collection.LinearSeqOptimized.foldLeft$(LinearSeqOptimized.scala:122)
		at scala.collection.immutable.List.foldLeft(List.scala:91)
		at org.apache.spark.sql.catalyst.rules.RuleExecutor.$anonfun$execute$1(RuleExecutor.scala:208)
		at org.apache.spark.sql.catalyst.rules.RuleExecutor.$anonfun$execute$1$adapted(RuleExecutor.scala:200)
		at scala.collection.immutable.List.foreach(List.scala:431)
		at org.apache.spark.sql.catalyst.rules.RuleExecutor.execute(RuleExecutor.scala:200)
		at org.apache.spark.sql.catalyst.analysis.Analyzer.org$apache$spark$sql$catalyst$analysis$Analyzer$$executeSameContext(Analyzer.scala:222)
		at org.apache.spark.sql.catalyst.analysis.Analyzer.$anonfun$execute$1(Analyzer.scala:218)
		at org.apache.spark.sql.catalyst.analysis.AnalysisContext$.withNewAnalysisContext(Analyzer.scala:167)
		at org.apache.spark.sql.catalyst.analysis.Analyzer.execute(Analyzer.scala:218)
		at org.apache.spark.sql.catalyst.analysis.Analyzer.execute(Analyzer.scala:182)
		at org.apache.spark.sql.catalyst.rules.RuleExecutor.$anonfun$executeAndTrack$1(RuleExecutor.scala:179)
		at org.apache.spark.sql.catalyst.QueryPlanningTracker$.withTracker(QueryPlanningTracker.scala:88)
		at org.apache.spark.sql.catalyst.rules.RuleExecutor.executeAndTrack(RuleExecutor.scala:179)
		at org.apache.spark.sql.catalyst.analysis.Analyzer.$anonfun$executeAndCheck$1(Analyzer.scala:203)
		at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper$.markInAnalyzer(AnalysisHelper.scala:330)
		at org.apache.spark.sql.catalyst.analysis.Analyzer.executeAndCheck(Analyzer.scala:202)
		at org.apache.spark.sql.execution.QueryExecution.$anonfun$analyzed$1(QueryExecution.scala:75)
		at org.apache.spark.sql.catalyst.QueryPlanningTracker.measurePhase(QueryPlanningTracker.scala:111)
		at org.apache.spark.sql.execution.QueryExecution.$anonfun$executePhase$1(QueryExecution.scala:183)
		at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:775)
		at org.apache.spark.sql.execution.QueryExecution.executePhase(QueryExecution.scala:183)
		at org.apache.spark.sql.execution.QueryExecution.analyzed$lzycompute(QueryExecution.scala:75)
		at org.apache.spark.sql.execution.QueryExecution.analyzed(QueryExecution.scala:73)
		at org.apache.spark.sql.execution.QueryExecution.assertAnalyzed(QueryExecution.scala:65)
		at org.apache.spark.sql.Dataset$.$anonfun$ofRows$2(Dataset.scala:98)
		at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:775)
		at org.apache.spark.sql.Dataset$.ofRows(Dataset.scala:96)
		at org.apache.spark.sql.SparkSession.$anonfun$sql$1(SparkSession.scala:618)
		at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:775)
		at org.apache.spark.sql.SparkSession.sql(SparkSession.scala:613)
		at com.apex.spark.transform.Sql.callCreateTable(Sql.java:582)
		... 17 more
	25/08/01 15:06:58 INFO SparkContext: Invoking stop() from shutdown hook
	25/08/01 15:06:58 INFO SparkUI: Stopped Spark web UI at http://trino.master:4040
	25/08/01 15:06:58 INFO SparkMetricsListener: actually generate task report all clocks are in nanoseconds 
	25/08/01 15:06:58 INFO PrintUtil: 
	**********************Reports***********************
	tasksCompleted            |  0
	totalOutputBytes          |  0
	stageCompletionTime       |  0.0ns
	totalInputBytes           |  0
	shuffleReadBytes          |  0
	shuffleWriteBytes         |  0
	jvmGCTime                 |  0ms
	executorRuntime           |  0s
	taskEndReason             |  
	stageSubmissionTime       |  0.0ns
	resultSize                |  0byte
	recordsWritten            |  0
	peakExecutionMemory       |  0byte
	jobName                   |  【每日】网上开户耗时统计
	numTasks                  |  0
	executorCpuTime           |  0ms
	recordsRead               |  0
	shuffleRecordsRead        |  0
	jobRuntime                |  0.0s
	stagesCompleted           |  0
	jobsCompleted             |  0
	createTime                |  2025-08-01 15:06:35
	failureReason             |  {}
	applicationId             |  local-1754031997258
	stageAttemptNumber        |  0
	shuffleRecordsWritten     |  0
	*********************Reports************************
	
	25/08/01 15:06:58 INFO LogMetricDataCollector: Print consumer closed.
	25/08/01 15:06:58 INFO MapOutputTrackerMasterEndpoint: MapOutputTrackerMasterEndpoint stopped!
	25/08/01 15:06:58 INFO MemoryStore: MemoryStore cleared
	25/08/01 15:06:58 INFO BlockManager: BlockManager stopped
	25/08/01 15:06:58 INFO BlockManagerMaster: BlockManagerMaster stopped
	25/08/01 15:06:58 INFO OutputCommitCoordinator$OutputCommitCoordinatorEndpoint: OutputCommitCoordinator stopped!
	25/08/01 15:06:58 INFO SparkContext: Successfully stopped SparkContext
	25/08/01 15:06:58 INFO ShutdownHookManager: Shutdown hook called
	25/08/01 15:06:58 INFO ShutdownHookManager: Deleting directory /tmp/spark-eb005031-8f7b-4508-9178-dc17f2206d67
	25/08/01 15:06:58 INFO ShutdownHookManager: Deleting directory /tmp/spark-818f2ebb-8110-4486-906c-8a363783869b
[INFO] 2025-08-01 15:06:58.724 TaskLogLogger-class org.apache.dolphinscheduler.plugin.task.shell.ShellTask:[63] - FINALIZE_SESSION
